#include <QGuiApplication>
#include <QQmlApplicationEngine>
#include <QQmlContext>
#include "devicemodel.h"
void setUpModel(DeviceTreeModel& model){
    static QString devices[3] = {
        "视讯",
        "H3-",
        "VST-"
    };
    static QString lan = "LAN-";
    for(int i = 0;i<5;i++){
        auto tmplan = lan + QString::number(i);
        for(auto& str: devices){
            auto tmpdev = str + QString::number(i);
            model.addDevice(tmplan,tmpdev);
        }
    }
    model.getSize();
}

int main(int argc, char *argv[])
{
    QCoreApplication::setAttribute(Qt::AA_EnableHighDpiScaling);

    QGuiApplication app(argc, argv);

    DeviceTreeModel model;
    setUpModel(model);
    model.getSize();
    QQmlApplicationEngine engine;
    const QUrl url(QStringLiteral("qrc:/main.qml"));

    engine.rootContext()->setContextProperty("deviceModel", &model);

    QObject::connect(&engine, &QQmlApplicationEngine::objectCreated,
                     &app, [url](QObject *obj, const QUrl &objUrl) {
        if (!obj && url == objUrl)
            QCoreApplication::exit(-1);
    }, Qt::QueuedConnection);
    engine.load(url);

    return app.exec();
}
