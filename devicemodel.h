#ifndef DEVICEMODEL_H
#define DEVICEMODEL_H

#include <QObject>
#include <QString>
#include <QAbstractItemModel>
class TreeItem {
public:
    TreeItem(TreeItem *parent = nullptr)
        : m_parent(parent)
    {
        if(parent) parent->appendChild(this);
    }

    ~TreeItem(){ qDeleteAll(m_children); }

    enum RoleType{
        LanType = Qt::UserRole + 1,
        DeviceType
    };

    void appendChild(TreeItem *child) { m_children.append(child); }
    TreeItem* child(int row) const {
        if(row >= m_children.size())
            return nullptr;
        return m_children.value(row);
    }
    int childCount() const { return m_children.size(); }

    int row() const { return m_parent ? m_parent->m_children.indexOf(const_cast<TreeItem*>(this)) : 0; }
    int columnCount() const { return 1; }
    TreeItem* parentItem() const { return m_parent; }

    //QVariant data(const RoleType& roleType) const { return m_data.first == roleType ? m_data.second : QVariant(); }
    QVariant data() const { return m_data.second; }
    RoleType roleType() const { return m_data.first; }

    void setData(const RoleType& roleType, const QString& data) { m_data = {roleType, data}; }


private:
    std::pair<RoleType,QString> m_data;   //存储数据
    //QVariantMap m_data;
    TreeItem *m_parent = nullptr;
    QList<TreeItem*> m_children;
};

class DeviceTreeModel : public QAbstractItemModel{
    Q_OBJECT
public:
    explicit  DeviceTreeModel(QObject* parent = nullptr);
    ~DeviceTreeModel();
    QModelIndex index(int row, int column, const QModelIndex &parent = QModelIndex()) const override;
    QModelIndex parent(const QModelIndex &child) const override;

    void addDevice(const QString& lanName,const QString& deviceName);
    void removeDevice(const QString& LanName, const TreeItem& item);

    int rowCount(const QModelIndex &parent = QModelIndex()) const override;
    int columnCount(const QModelIndex &parent = QModelIndex()) const override;
    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;

    QHash<int, QByteArray> roleNames() const override;
    int getSize();
private:
    TreeItem *m_rootItem;
    QMap<QString,TreeItem*> m_lanGroupMap;
};

#endif // LANDEVICEMODEL_H
