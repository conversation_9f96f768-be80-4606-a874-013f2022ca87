import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQml.Models 2.15

// 解决Flickable嵌套TreeView滚动条冲突的组件
Rectangle {
    id: root
    width: 800
    height: 600
    color: "#f5f5f5"
    
    // 示例数据模型
    TreeModel {
        id: treeModel
        TreeElement {
            display: "LAN 1"
            TreeElement { display: "设备01" }
            TreeElement { display: "设备02" }
            TreeElement { display: "设备03" }
        }
        TreeElement {
            display: "LAN 2"
            TreeElement { display: "设备04" }
            TreeElement { display: "设备05" }
        }
        TreeElement {
            display: "LAN 3"
            TreeElement { display: "设备06" }
            TreeElement { display: "设备07" }
            TreeElement { display: "设备08" }
            TreeElement { display: "设备09" }
        }
    }
    
    // 解决方案1: 使用ScrollView替代Flickable
    ScrollView {
        id: scrollView
        anchors.fill: parent
        anchors.margins: 20
        
        // 关键：禁用ScrollView的水平滚动，只保留垂直滚动
        ScrollBar.horizontal.policy: ScrollBar.AlwaysOff
        ScrollBar.vertical.policy: ScrollBar.AsNeeded
        
        // 自定义垂直滚动条样式，确保可以手动拖拽
        ScrollBar.vertical: ScrollBar {
            id: verticalScrollBar
            policy: ScrollBar.AsNeeded
            minimumSize: 0.1
            
            // 确保滚动条可见且可交互
            background: Rectangle {
                color: "#e0e0e0"
                radius: 6
                implicitWidth: 12
            }
            
            contentItem: Rectangle {
                color: verticalScrollBar.pressed ? "#606060" : 
                       verticalScrollBar.hovered ? "#808080" : "#a0a0a0"
                radius: 6
                
                // 确保有最小尺寸便于拖拽
                implicitWidth: 12
                implicitHeight: 20
                
                // 添加鼠标区域确保响应
                MouseArea {
                    anchors.fill: parent
                    acceptedButtons: Qt.LeftButton
                    
                    property real startY: 0
                    property real startPosition: 0
                    
                    onPressed: {
                        startY = mouse.y
                        startPosition = verticalScrollBar.position
                    }
                    
                    onPositionChanged: {
                        if (pressed) {
                            var delta = mouse.y - startY
                            var maxDelta = verticalScrollBar.height - verticalScrollBar.contentItem.height
                            var newPosition = startPosition + (delta / maxDelta) * (1.0 - verticalScrollBar.size)
                            verticalScrollBar.position = Math.max(0, Math.min(1.0 - verticalScrollBar.size, newPosition))
                        }
                    }
                }
            }
        }
        
        TreeView {
            id: treeView
            model: treeModel
            
            // 关键：设置合适的尺寸策略
            implicitWidth: scrollView.availableWidth
            implicitHeight: contentHeight
            
            // 禁用TreeView自身的交互滚动，让ScrollView处理
            interactive: false
            
            delegate: TreeViewDelegate {
                id: treeDelegate
                
                // 确保delegate有合适的尺寸
                implicitWidth: treeView.width
                implicitHeight: 40
                
                Rectangle {
                    anchors.fill: parent
                    color: treeDelegate.isTreeNode ? "#e3f2fd" : "#ffffff"
                    border.color: "#cccccc"
                    border.width: 1
                    radius: 4
                    
                    // 缩进指示器
                    Rectangle {
                        width: 2
                        height: parent.height
                        color: "#2196f3"
                        visible: treeDelegate.depth > 0
                        x: treeDelegate.depth * 20 - 10
                    }
                    
                    Row {
                        anchors.left: parent.left
                        anchors.leftMargin: treeDelegate.depth * 20 + 10
                        anchors.verticalCenter: parent.verticalCenter
                        spacing: 8
                        
                        // 展开/折叠指示器
                        Text {
                            text: treeDelegate.isTreeNode ? 
                                  (treeDelegate.expanded ? "▼" : "▶") : "●"
                            color: treeDelegate.isTreeNode ? "#2196f3" : "#666666"
                            font.pixelSize: 12
                            anchors.verticalCenter: parent.verticalCenter
                        }
                        
                        Text {
                            text: model.display
                            color: "#333333"
                            font.pixelSize: 14
                            font.bold: treeDelegate.isTreeNode
                            anchors.verticalCenter: parent.verticalCenter
                        }
                    }
                    
                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            if (treeDelegate.isTreeNode) {
                                treeDelegate.expanded = !treeDelegate.expanded
                            }
                        }
                        
                        // 防止事件传播到ScrollView
                        onPressed: mouse.accepted = true
                        onReleased: mouse.accepted = true
                    }
                }
            }
        }
    }
    
    // 解决方案2: 如果必须使用Flickable，这是修复版本
    /*
    Flickable {
        id: flickable
        anchors.fill: parent
        anchors.margins: 20
        
        // 设置内容尺寸
        contentWidth: treeView2.implicitWidth
        contentHeight: treeView2.implicitHeight
        
        // 只允许垂直滚动
        flickableDirection: Flickable.VerticalFlick
        
        // 关键：禁用边界行为，避免冲突
        boundsBehavior: Flickable.StopAtBounds
        
        TreeView {
            id: treeView2
            width: flickable.width
            model: treeModel
            
            // 关键：完全禁用TreeView的交互
            interactive: false
            
            delegate: TreeViewDelegate {
                implicitWidth: treeView2.width
                implicitHeight: 40
                
                Rectangle {
                    anchors.fill: parent
                    color: parent.isTreeNode ? "#f0f0f0" : "white"
                    border.color: "#cccccc"
                    
                    Text {
                        anchors.left: parent.left
                        anchors.leftMargin: parent.depth * 20 + 10
                        anchors.verticalCenter: parent.verticalCenter
                        text: model.display
                    }
                    
                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            if (parent.parent.isTreeNode) {
                                parent.parent.expanded = !parent.parent.expanded
                            }
                        }
                        
                        // 关键：阻止事件传播
                        propagateComposedEvents: false
                    }
                }
            }
        }
        
        // 自定义滚动条
        Rectangle {
            id: scrollBar
            anchors.right: parent.right
            anchors.top: parent.top
            anchors.bottom: parent.bottom
            width: 12
            color: "#e0e0e0"
            radius: 6
            visible: flickable.contentHeight > flickable.height
            
            Rectangle {
                id: scrollHandle
                width: parent.width
                height: Math.max(20, parent.height * (flickable.height / flickable.contentHeight))
                color: scrollMouseArea.pressed ? "#606060" : 
                       scrollMouseArea.containsMouse ? "#808080" : "#a0a0a0"
                radius: 6
                
                y: flickable.contentY * (parent.height - height) / (flickable.contentHeight - flickable.height)
                
                MouseArea {
                    id: scrollMouseArea
                    anchors.fill: parent
                    hoverEnabled: true
                    
                    property real startY: 0
                    property real startContentY: 0
                    
                    onPressed: {
                        startY = mouse.y
                        startContentY = flickable.contentY
                    }
                    
                    onPositionChanged: {
                        if (pressed) {
                            var delta = mouse.y - startY
                            var maxScroll = flickable.contentHeight - flickable.height
                            var scrollRatio = delta / (scrollBar.height - scrollHandle.height)
                            flickable.contentY = Math.max(0, Math.min(maxScroll, 
                                                startContentY + scrollRatio * maxScroll))
                        }
                    }
                }
            }
        }
    }
    */
}
