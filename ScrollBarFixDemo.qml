import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Window 2.15

// 演示如何解决Flickable嵌套组件的滚动条问题
ApplicationWindow {
    id: window
    width: 900
    height: 700
    visible: true
    title: "滚动条修复演示"
    
    Row {
        anchors.fill: parent
        spacing: 10
        
        // 左侧：问题演示 - 有滚动条冲突
        Rectangle {
            width: parent.width / 2 - 5
            height: parent.height
            color: "#ffebee"
            border.color: "#f44336"
            border.width: 2
            
            Text {
                anchors.top: parent.top
                anchors.horizontalCenter: parent.horizontalCenter
                anchors.margins: 10
                text: "❌ 问题版本 - 滚动条冲突"
                font.bold: true
                color: "#d32f2f"
            }
            
            Flickable {
                id: problemFlickable
                anchors.fill: parent
                anchors.margins: 40
                anchors.topMargin: 50
                
                contentWidth: problemContent.width
                contentHeight: problemContent.height
                
                // 问题：嵌套的ListView也有自己的滚动
                Column {
                    id: problemContent
                    width: problemFlickable.width
                    spacing: 10
                    
                    Repeater {
                        model: 5
                        delegate: Rectangle {
                            width: parent.width
                            height: 200
                            color: "#ffffff"
                            border.color: "#cccccc"
                            border.width: 1
                            radius: 8
                            
                            Column {
                                anchors.fill: parent
                                anchors.margins: 10
                                
                                Text {
                                    text: "分组 " + (index + 1)
                                    font.bold: true
                                    color: "#333333"
                                }
                                
                                // 问题：这个ListView会与外层Flickable冲突
                                ListView {
                                    width: parent.width
                                    height: 150
                                    model: 8
                                    interactive: true  // 问题所在：内层也可交互
                                    
                                    delegate: Rectangle {
                                        width: parent.width
                                        height: 25
                                        color: index % 2 ? "#f5f5f5" : "#ffffff"
                                        Text {
                                            anchors.centerIn: parent
                                            text: "项目 " + (index + 1)
                                        }
                                    }
                                    
                                    ScrollBar.vertical: ScrollBar {
                                        // 内层滚动条与外层冲突
                                    }
                                }
                            }
                        }
                    }
                }
                
                ScrollBar.vertical: ScrollBar {
                    // 外层滚动条
                }
            }
        }
        
        // 右侧：解决方案 - 修复后的版本
        Rectangle {
            width: parent.width / 2 - 5
            height: parent.height
            color: "#e8f5e8"
            border.color: "#4caf50"
            border.width: 2
            
            Text {
                anchors.top: parent.top
                anchors.horizontalCenter: parent.horizontalCenter
                anchors.margins: 10
                text: "✅ 修复版本 - 滚动条正常"
                font.bold: true
                color: "#388e3c"
            }
            
            ScrollView {
                id: fixedScrollView
                anchors.fill: parent
                anchors.margins: 40
                anchors.topMargin: 50
                
                // 关键修复1：明确滚动策略
                ScrollBar.horizontal.policy: ScrollBar.AlwaysOff
                ScrollBar.vertical.policy: ScrollBar.AsNeeded
                
                // 关键修复2：自定义滚动条样式
                ScrollBar.vertical: ScrollBar {
                    id: customScrollBar
                    policy: ScrollBar.AsNeeded
                    
                    background: Rectangle {
                        color: "#f0f0f0"
                        radius: 8
                        implicitWidth: 16
                        border.color: "#d0d0d0"
                        border.width: 1
                    }
                    
                    contentItem: Rectangle {
                        color: customScrollBar.pressed ? "#4caf50" : 
                               customScrollBar.hovered ? "#66bb6a" : "#81c784"
                        radius: 8
                        implicitWidth: 14
                        implicitHeight: 40
                        
                        // 拖拽指示器
                        Rectangle {
                            anchors.centerIn: parent
                            width: 10
                            height: 3
                            color: "white"
                            opacity: 0.8
                            radius: 1.5
                        }
                        
                        // 确保滚动条响应鼠标事件
                        MouseArea {
                            anchors.fill: parent
                            acceptedButtons: Qt.LeftButton
                            
                            property real startY: 0
                            property real startPosition: 0
                            
                            onPressed: {
                                startY = mouse.y
                                startPosition = customScrollBar.position
                            }
                            
                            onPositionChanged: {
                                if (pressed) {
                                    var delta = mouse.y - startY
                                    var availableHeight = customScrollBar.height - height
                                    if (availableHeight > 0) {
                                        var ratio = delta / availableHeight
                                        var maxPosition = 1.0 - customScrollBar.size
                                        var newPosition = startPosition + ratio * maxPosition
                                        customScrollBar.position = Math.max(0, Math.min(maxPosition, newPosition))
                                    }
                                }
                            }
                        }
                    }
                }
                
                Column {
                    id: fixedContent
                    width: fixedScrollView.availableWidth
                    spacing: 10
                    
                    Repeater {
                        model: 5
                        delegate: Rectangle {
                            width: parent.width
                            height: 200
                            color: "#ffffff"
                            border.color: "#cccccc"
                            border.width: 1
                            radius: 8
                            
                            Column {
                                anchors.fill: parent
                                anchors.margins: 10
                                
                                Text {
                                    text: "分组 " + (index + 1)
                                    font.bold: true
                                    color: "#333333"
                                }
                                
                                // 关键修复3：禁用内层交互
                                ListView {
                                    width: parent.width
                                    height: 150
                                    model: 8
                                    interactive: false  // 修复：禁用内层交互
                                    
                                    delegate: Rectangle {
                                        width: parent.width
                                        height: 25
                                        color: index % 2 ? "#f5f5f5" : "#ffffff"
                                        border.color: "#e0e0e0"
                                        border.width: 1
                                        
                                        Text {
                                            anchors.centerIn: parent
                                            text: "项目 " + (index + 1)
                                            color: "#666666"
                                        }
                                        
                                        // 保持点击功能
                                        MouseArea {
                                            anchors.fill: parent
                                            onClicked: {
                                                parent.color = parent.color === "#e3f2fd" ? 
                                                              (index % 2 ? "#f5f5f5" : "#ffffff") : "#e3f2fd"
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    // 底部说明
    Rectangle {
        anchors.bottom: parent.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        height: 60
        color: "#f5f5f5"
        border.color: "#cccccc"
        border.width: 1
        
        Text {
            anchors.centerIn: parent
            text: "修复要点：1) 使用ScrollView替代Flickable  2) 禁用内层组件的interactive  3) 自定义滚动条样式确保可拖拽"
            font.pixelSize: 12
            color: "#666666"
            wrapMode: Text.WordWrap
        }
    }
}
