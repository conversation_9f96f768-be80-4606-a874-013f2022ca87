
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Windows - 10.0.19045 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/clang.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.exe"
      
      The CXX compiler identification is Clang, found in:
        C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/4.0.2/CompilerIdCXX/a.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:1322 (message)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:250 (CMAKE_DETERMINE_MSVC_SHOWINCLUDES_PREFIX)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Detecting CXX compiler /showIncludes prefix:
        
  -
    kind: "message-v1"
    backtrace:
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:1322 (message)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:250 (CMAKE_DETERMINE_MSVC_SHOWINCLUDES_PREFIX)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Detecting CXX compiler /showIncludes prefix:
        
  -
    kind: "try_compile-v1"
    backtrace:
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-e1wz7k"
      binary: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-e1wz7k"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/clang-scan-deps.exe"
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-O0 -g -Xclang -gcodeview"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-e1wz7k'
        
        Run Build Command(s): "C:/PROGRA~1/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v cmTC_8c291
        [1/2] "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe"   -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -MD -MT CMakeFiles/cmTC_8c291.dir/CMakeCXXCompilerABI.cpp.obj -MF CMakeFiles\\cmTC_8c291.dir\\CMakeCXXCompilerABI.cpp.obj.d -o CMakeFiles/cmTC_8c291.dir/CMakeCXXCompilerABI.cpp.obj -c F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeCXXCompilerABI.cpp
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe" -nostartfiles -nostdlib -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -v -Xlinker /subsystem:console -fuse-ld=lld-link CMakeFiles/cmTC_8c291.dir/CMakeCXXCompilerABI.cpp.obj -o cmTC_8c291.exe -Xlinker /MANIFEST:EMBED -Xlinker /implib:cmTC_8c291.lib -Xlinker /pdb:cmTC_8c291.pdb -Xlinker /version:0.0     && cd ."
        clang version 19.1.1
        Target: x86_64-pc-windows-msvc
        Thread model: posix
        InstalledDir: C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin
         "C:\\\\Program Files\\\\Microsoft Visual Studio\\\\2022\\\\Community\\\\VC\\\\Tools\\\\Llvm\\\\x64\\\\bin\\\\lld-link" -out:cmTC_8c291.exe "-libpath:C:\\\\Program Files\\\\Microsoft Visual Studio\\\\2022\\\\Community\\\\VC\\\\Tools\\\\Llvm\\\\x64\\\\lib\\\\clang\\\\19\\\\lib\\\\windows" -nologo -debug /subsystem:console CMakeFiles/cmTC_8c291.dir/CMakeCXXCompilerABI.cpp.obj /MANIFEST:EMBED /implib:cmTC_8c291.lib /pdb:cmTC_8c291.pdb /version:0.0\x0d
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/lld-link
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/lld-link" "-v"
      lld-link: warning: ignoring unknown argument '-v'
      lld-link: error: no input files
  -
    kind: "message-v1"
    backtrace:
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/lld-link" "-V"
      lld-link: warning: ignoring unknown argument '-V'
      lld-link: error: no input files
  -
    kind: "message-v1"
    backtrace:
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/lld-link" "--version"
      LLD 19.1.1
  -
    kind: "try_compile-v1"
    backtrace:
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/Internal/FeatureTesting.cmake:34 (try_compile)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/Internal/FeatureTesting.cmake:87 (_record_compiler_features)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/Compiler/CMakeCommonCompilerMacros.cmake:124 (_record_compiler_features_cxx)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerSupport.cmake:70 (cmake_record_cxx_compile_features)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:83 (CMAKE_DETERMINE_COMPILER_SUPPORT)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compile features"
    directories:
      source: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-d3ygrc"
      binary: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-d3ygrc"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/clang-scan-deps.exe"
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-O0 -g -Xclang -gcodeview"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_FEATURE_TEST"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-d3ygrc'
        
        Run Build Command(s): "C:/PROGRA~1/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v cmTC_1f4e3
        [1/2] "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe"   -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd   -std=c++14 -MD -MT CMakeFiles/cmTC_1f4e3.dir/feature_tests.cxx.obj -MF CMakeFiles\\cmTC_1f4e3.dir\\feature_tests.cxx.obj.d -o CMakeFiles/cmTC_1f4e3.dir/feature_tests.cxx.obj -c C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-d3ygrc/feature_tests.cxx
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe" -nostartfiles -nostdlib -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -Xlinker /subsystem:console -fuse-ld=lld-link CMakeFiles/cmTC_1f4e3.dir/feature_tests.cxx.obj -o cmTC_1f4e3.exe -Xlinker /MANIFEST:EMBED -Xlinker /implib:cmTC_1f4e3.lib -Xlinker /pdb:cmTC_1f4e3.pdb -Xlinker /version:0.0   -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -loldnames  && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/Internal/FeatureTesting.cmake:34 (try_compile)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/Internal/FeatureTesting.cmake:87 (_record_compiler_features)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/Compiler/CMakeCommonCompilerMacros.cmake:132 (_record_compiler_features_cxx)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerSupport.cmake:70 (cmake_record_cxx_compile_features)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:83 (CMAKE_DETERMINE_COMPILER_SUPPORT)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compile features"
    directories:
      source: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-o5zxkv"
      binary: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-o5zxkv"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/clang-scan-deps.exe"
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-O0 -g -Xclang -gcodeview"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_FEATURE_TEST"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-o5zxkv'
        
        Run Build Command(s): "C:/PROGRA~1/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v cmTC_0c0b4
        [1/2] "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe"   -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd   -std=c++14 -MD -MT CMakeFiles/cmTC_0c0b4.dir/feature_tests.cxx.obj -MF CMakeFiles\\cmTC_0c0b4.dir\\feature_tests.cxx.obj.d -o CMakeFiles/cmTC_0c0b4.dir/feature_tests.cxx.obj -c C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-o5zxkv/feature_tests.cxx
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe" -nostartfiles -nostdlib -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -Xlinker /subsystem:console -fuse-ld=lld-link CMakeFiles/cmTC_0c0b4.dir/feature_tests.cxx.obj -o cmTC_0c0b4.exe -Xlinker /MANIFEST:EMBED -Xlinker /implib:cmTC_0c0b4.lib -Xlinker /pdb:cmTC_0c0b4.pdb -Xlinker /version:0.0   -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -loldnames  && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CheckCXXSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/FindThreads.cmake:99 (check_cxx_source_compiles)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake:78 (find_package)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Dependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Config.cmake:168 (include)"
      - "CMakeLists.txt:8 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-bkfcq0"
      binary: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-bkfcq0"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/clang-scan-deps.exe"
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-O0 -g -Xclang -gcodeview"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6;C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-bkfcq0'
        
        Run Build Command(s): "C:/PROGRA~1/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v cmTC_5666a
        [1/2] "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe" -DCMAKE_HAVE_LIBC_PTHREAD  -O0 -g -Xclang -gcodeview -std=gnu++17 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -MD -MT CMakeFiles/cmTC_5666a.dir/src.cxx.obj -MF CMakeFiles\\cmTC_5666a.dir\\src.cxx.obj.d -o CMakeFiles/cmTC_5666a.dir/src.cxx.obj -c C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-bkfcq0/src.cxx
        FAILED: CMakeFiles/cmTC_5666a.dir/src.cxx.obj 
        "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe" -DCMAKE_HAVE_LIBC_PTHREAD  -O0 -g -Xclang -gcodeview -std=gnu++17 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -MD -MT CMakeFiles/cmTC_5666a.dir/src.cxx.obj -MF CMakeFiles\\cmTC_5666a.dir\\src.cxx.obj.d -o CMakeFiles/cmTC_5666a.dir/src.cxx.obj -c C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-bkfcq0/src.cxx
        C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-bkfcq0/src.cxx:1:10: fatal error: 'pthread.h' file not found\x0d
            1 | #include <pthread.h>\x0d
              |          ^~~~~~~~~~~\x0d
        1 error generated.\x0d
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/FindThreads.cmake:136 (try_compile)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/FindThreads.cmake:169 (_threads_check_flag_pthread)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake:78 (find_package)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Dependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Config.cmake:168 (include)"
      - "CMakeLists.txt:8 (find_package)"
    checks:
      - "Check if compiler accepts -pthread"
    directories:
      source: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-rcbx57"
      binary: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-rcbx57"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/clang-scan-deps.exe"
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-O0 -g -Xclang -gcodeview"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6;C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "THREADS_HAVE_PTHREAD_ARG"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-rcbx57'
        
        Run Build Command(s): "C:/PROGRA~1/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v cmTC_94641
        [1/2] "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe"   -O0 -g -Xclang -gcodeview -std=gnu++17 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -MD -MT CMakeFiles/cmTC_94641.dir/CheckForPthreads.cxx.obj -MF CMakeFiles\\cmTC_94641.dir\\CheckForPthreads.cxx.obj.d -o CMakeFiles/cmTC_94641.dir/CheckForPthreads.cxx.obj -c C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-rcbx57/CheckForPthreads.cxx
        FAILED: CMakeFiles/cmTC_94641.dir/CheckForPthreads.cxx.obj 
        "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe"   -O0 -g -Xclang -gcodeview -std=gnu++17 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -MD -MT CMakeFiles/cmTC_94641.dir/CheckForPthreads.cxx.obj -MF CMakeFiles\\cmTC_94641.dir\\CheckForPthreads.cxx.obj.d -o CMakeFiles/cmTC_94641.dir/CheckForPthreads.cxx.obj -c C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-rcbx57/CheckForPthreads.cxx
        C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-rcbx57/CheckForPthreads.cxx:1:10: fatal error: 'pthread.h' file not found\x0d
            1 | #include <pthread.h>\x0d
              |          ^~~~~~~~~~~\x0d
        1 error generated.\x0d
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CheckLibraryExists.cmake:112 (try_compile)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/FindThreads.cmake:112 (check_library_exists)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake:78 (find_package)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Dependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Config.cmake:168 (include)"
      - "CMakeLists.txt:8 (find_package)"
    checks:
      - "Looking for pthread_create in pthreads"
    directories:
      source: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-yr6f2b"
      binary: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-yr6f2b"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/clang-scan-deps.exe"
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-O0 -g -Xclang -gcodeview"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6;C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-yr6f2b'
        
        Run Build Command(s): "C:/PROGRA~1/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v cmTC_27c88
        [1/2] "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe"   -DCHECK_FUNCTION_EXISTS=pthread_create -O0 -g -Xclang -gcodeview -std=gnu++17 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -MD -MT CMakeFiles/cmTC_27c88.dir/CheckFunctionExists.cxx.obj -MF CMakeFiles\\cmTC_27c88.dir\\CheckFunctionExists.cxx.obj.d -o CMakeFiles/cmTC_27c88.dir/CheckFunctionExists.cxx.obj -c C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-yr6f2b/CheckFunctionExists.cxx
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe" -nostartfiles -nostdlib -DCHECK_FUNCTION_EXISTS=pthread_create -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -Xlinker /subsystem:console -fuse-ld=lld-link CMakeFiles/cmTC_27c88.dir/CheckFunctionExists.cxx.obj -o cmTC_27c88.exe -Xlinker /MANIFEST:EMBED -Xlinker /implib:cmTC_27c88.lib -Xlinker /pdb:cmTC_27c88.pdb -Xlinker /version:0.0   -lpthreads.lib  -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -loldnames  && cd ."
        FAILED: cmTC_27c88.exe 
        C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe" -nostartfiles -nostdlib -DCHECK_FUNCTION_EXISTS=pthread_create -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -Xlinker /subsystem:console -fuse-ld=lld-link CMakeFiles/cmTC_27c88.dir/CheckFunctionExists.cxx.obj -o cmTC_27c88.exe -Xlinker /MANIFEST:EMBED -Xlinker /implib:cmTC_27c88.lib -Xlinker /pdb:cmTC_27c88.pdb -Xlinker /version:0.0   -lpthreads.lib  -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -loldnames  && cd ."
        lld-link: error: could not open 'pthreads.lib': no such file or directory\x0d
        clang: error: linker command failed with exit code 1 (use -v to see invocation)\x0d
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CheckLibraryExists.cmake:112 (try_compile)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/FindThreads.cmake:112 (check_library_exists)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake:78 (find_package)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Dependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Config.cmake:168 (include)"
      - "CMakeLists.txt:8 (find_package)"
    checks:
      - "Looking for pthread_create in pthread"
    directories:
      source: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-v9nzmi"
      binary: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-v9nzmi"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/clang-scan-deps.exe"
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-O0 -g -Xclang -gcodeview"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6;C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-v9nzmi'
        
        Run Build Command(s): "C:/PROGRA~1/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v cmTC_eee8a
        [1/2] "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe"   -DCHECK_FUNCTION_EXISTS=pthread_create -O0 -g -Xclang -gcodeview -std=gnu++17 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -MD -MT CMakeFiles/cmTC_eee8a.dir/CheckFunctionExists.cxx.obj -MF CMakeFiles\\cmTC_eee8a.dir\\CheckFunctionExists.cxx.obj.d -o CMakeFiles/cmTC_eee8a.dir/CheckFunctionExists.cxx.obj -c C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-v9nzmi/CheckFunctionExists.cxx
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe" -nostartfiles -nostdlib -DCHECK_FUNCTION_EXISTS=pthread_create -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -Xlinker /subsystem:console -fuse-ld=lld-link CMakeFiles/cmTC_eee8a.dir/CheckFunctionExists.cxx.obj -o cmTC_eee8a.exe -Xlinker /MANIFEST:EMBED -Xlinker /implib:cmTC_eee8a.lib -Xlinker /pdb:cmTC_eee8a.pdb -Xlinker /version:0.0   -lpthread.lib  -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -loldnames  && cd ."
        FAILED: cmTC_eee8a.exe 
        C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe" -nostartfiles -nostdlib -DCHECK_FUNCTION_EXISTS=pthread_create -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -Xlinker /subsystem:console -fuse-ld=lld-link CMakeFiles/cmTC_eee8a.dir/CheckFunctionExists.cxx.obj -o cmTC_eee8a.exe -Xlinker /MANIFEST:EMBED -Xlinker /implib:cmTC_eee8a.lib -Xlinker /pdb:cmTC_eee8a.pdb -Xlinker /version:0.0   -lpthread.lib  -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -loldnames  && cd ."
        lld-link: error: could not open 'pthread.lib': no such file or directory\x0d
        clang: error: linker command failed with exit code 1 (use -v to see invocation)\x0d
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CheckCXXSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/FindWrapAtomic.cmake:36 (check_cxx_source_compiles)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake:78 (find_package)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake:45 (include)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Config.cmake:218 (find_package)"
      - "CMakeLists.txt:8 (find_package)"
    checks:
      - "Performing Test HAVE_STDATOMIC"
    directories:
      source: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-pp2z1c"
      binary: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-pp2z1c"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/clang-scan-deps.exe"
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-O0 -g -Xclang -gcodeview"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6;C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "HAVE_STDATOMIC"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-pp2z1c'
        
        Run Build Command(s): "C:/PROGRA~1/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v cmTC_ffd6e
        [1/2] "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe" -DHAVE_STDATOMIC  -O0 -g -Xclang -gcodeview -std=gnu++17 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -MD -MT CMakeFiles/cmTC_ffd6e.dir/src.cxx.obj -MF CMakeFiles\\cmTC_ffd6e.dir\\src.cxx.obj.d -o CMakeFiles/cmTC_ffd6e.dir/src.cxx.obj -c C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-pp2z1c/src.cxx
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe" -nostartfiles -nostdlib -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -Xlinker /subsystem:console -fuse-ld=lld-link CMakeFiles/cmTC_ffd6e.dir/src.cxx.obj -o cmTC_ffd6e.exe -Xlinker /MANIFEST:EMBED -Xlinker /implib:cmTC_ffd6e.lib -Xlinker /pdb:cmTC_ffd6e.pdb -Xlinker /version:0.0   -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -loldnames  && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "message-v1"
    backtrace:
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Windows - 6.2.9200 - AMD64
  -
    kind: "find-v1"
    backtrace:
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "E:/mingw64/share/cmake-4.1/Modules/"
    found: "E:/mingw64/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\VCPackages"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TestWindow"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TeamFoundation\\Team Explorer"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\bin\\Roslyn"
        - "C:\\Program Files (x86)\\Microsoft SDKs\\Windows\\v10.0A\\bin\\NETFX 4.8 Tools\\x64\\"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Team Tools\\DiagnosticsHub\\Collector"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin"
        - "E:\\Windows Kits\\10\\bin\\10.0.26100.0\\\\x64"
        - "E:\\Windows Kits\\10\\bin\\\\x64"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\\\MSBuild\\Current\\Bin\\amd64"
        - "C:\\Windows\\Microsoft.NET\\Framework64\\v4.0.30319"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\Tools\\"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "D:\\banizip\\Bandizip"
        - "E:\\Bandizip"
        - "E:\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\dotnet\\"
        - "E:\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "E:\\mingw64\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin"
        - "D:\\audio\\ffmpeg-2025-03-17-git-5b9356f18e-full_build\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "F:\\cmake-4.0.2-windows-x86_64\\bin"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\Hostx64\\x64"
        - "C:\\Users\\<USER>\\AppData\\Local\\Muse Hub\\lib"
        - "C:\\Program Files\\I-SIMPA\\bin"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\Ninja"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\Linux\\bin\\ConnectionManagerExe"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\vcpkg"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/qmltest"
      ENV{INCLUDE}:
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include"
        - "E:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt"
        - "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\um"
        - "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\shared"
        - "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\winrt"
        - "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\cppwinrt"
        - "C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um"
  -
    kind: "message-v1"
    backtrace:
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/clang.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.exe"
      
      The CXX compiler identification is Clang, found in:
        C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/4.1.0/CompilerIdCXX/a.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:1341 (message)"
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:250 (CMAKE_DETERMINE_MSVC_SHOWINCLUDES_PREFIX)"
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Detecting CXX compiler /showIncludes prefix:
        
  -
    kind: "message-v1"
    backtrace:
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:1341 (message)"
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:250 (CMAKE_DETERMINE_MSVC_SHOWINCLUDES_PREFIX)"
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Detecting CXX compiler /showIncludes prefix:
        
  -
    kind: "find-v1"
    backtrace:
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_READELF"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "llvm-readelf"
      - "readelf"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/x64/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/"
      - "E:/Windows Kits/10/bin/10.0.26100.0/x64/"
      - "E:/Windows Kits/10/bin/x64/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/"
      - "C:/Windows/Microsoft.NET/Framework64/v4.0.30319/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/Tools/"
      - "C:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "D:/banizip/Bandizip/"
      - "E:/Bandizip/"
      - "E:/Git/cmd/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/dotnet/"
      - "E:/Windows Kits/10/Windows Performance Toolkit/"
      - "E:/mingw64/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/"
      - "D:/audio/ffmpeg-2025-03-17-git-5b9356f18e-full_build/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "F:/cmake-4.0.2-windows-x86_64/bin/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Users/<USER>/AppData/Local/Muse Hub/lib/"
      - "C:/Program Files/I-SIMPA/bin/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/Linux/bin/ConnectionManagerExe/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/vcpkg/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/llvm-readelf.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/llvm-readelf.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/llvm-readelf"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/llvm-readelf.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/llvm-readelf.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/llvm-readelf"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/llvm-readelf.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/llvm-readelf.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/llvm-readelf"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/llvm-readelf.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/llvm-readelf.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/llvm-readelf"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/llvm-readelf.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/llvm-readelf.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/llvm-readelf"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/llvm-readelf.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/llvm-readelf.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/llvm-readelf"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/x64/llvm-readelf.com"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/x64/llvm-readelf.exe"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/x64/llvm-readelf"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/llvm-readelf.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/llvm-readelf.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/llvm-readelf"
      - "E:/Windows Kits/10/bin/10.0.26100.0/x64/llvm-readelf.com"
      - "E:/Windows Kits/10/bin/10.0.26100.0/x64/llvm-readelf.exe"
      - "E:/Windows Kits/10/bin/10.0.26100.0/x64/llvm-readelf"
      - "E:/Windows Kits/10/bin/x64/llvm-readelf.com"
      - "E:/Windows Kits/10/bin/x64/llvm-readelf.exe"
      - "E:/Windows Kits/10/bin/x64/llvm-readelf"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/llvm-readelf.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/llvm-readelf.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/llvm-readelf"
      - "C:/Windows/Microsoft.NET/Framework64/v4.0.30319/llvm-readelf.com"
      - "C:/Windows/Microsoft.NET/Framework64/v4.0.30319/llvm-readelf.exe"
      - "C:/Windows/Microsoft.NET/Framework64/v4.0.30319/llvm-readelf"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/llvm-readelf.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/llvm-readelf.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/llvm-readelf"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/Tools/llvm-readelf.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/Tools/llvm-readelf.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/Tools/llvm-readelf"
      - "C:/Program Files (x86)/VMware/VMware Workstation/bin/llvm-readelf.com"
      - "C:/Program Files (x86)/VMware/VMware Workstation/bin/llvm-readelf.exe"
      - "C:/Program Files (x86)/VMware/VMware Workstation/bin/llvm-readelf"
      - "C:/Windows/System32/llvm-readelf.com"
      - "C:/Windows/System32/llvm-readelf.exe"
      - "C:/Windows/System32/llvm-readelf"
      - "C:/Windows/llvm-readelf.com"
      - "C:/Windows/llvm-readelf.exe"
      - "C:/Windows/llvm-readelf"
      - "C:/Windows/System32/wbem/llvm-readelf.com"
      - "C:/Windows/System32/wbem/llvm-readelf.exe"
      - "C:/Windows/System32/wbem/llvm-readelf"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/llvm-readelf.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/llvm-readelf.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/llvm-readelf"
      - "C:/Windows/System32/OpenSSH/llvm-readelf.com"
      - "C:/Windows/System32/OpenSSH/llvm-readelf.exe"
      - "C:/Windows/System32/OpenSSH/llvm-readelf"
      - "D:/banizip/Bandizip/llvm-readelf.com"
      - "D:/banizip/Bandizip/llvm-readelf.exe"
      - "D:/banizip/Bandizip/llvm-readelf"
      - "E:/Bandizip/llvm-readelf.com"
      - "E:/Bandizip/llvm-readelf.exe"
      - "E:/Bandizip/llvm-readelf"
      - "E:/Git/cmd/llvm-readelf.com"
      - "E:/Git/cmd/llvm-readelf.exe"
      - "E:/Git/cmd/llvm-readelf"
      - "C:/Program Files/Docker/Docker/resources/bin/llvm-readelf.com"
      - "C:/Program Files/Docker/Docker/resources/bin/llvm-readelf.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/llvm-readelf"
      - "C:/ProgramData/chocolatey/bin/llvm-readelf.com"
      - "C:/ProgramData/chocolatey/bin/llvm-readelf.exe"
      - "C:/ProgramData/chocolatey/bin/llvm-readelf"
      - "C:/Program Files/dotnet/llvm-readelf.com"
      - "C:/Program Files/dotnet/llvm-readelf.exe"
      - "C:/Program Files/dotnet/llvm-readelf"
      - "E:/Windows Kits/10/Windows Performance Toolkit/llvm-readelf.com"
      - "E:/Windows Kits/10/Windows Performance Toolkit/llvm-readelf.exe"
      - "E:/Windows Kits/10/Windows Performance Toolkit/llvm-readelf"
      - "E:/mingw64/bin/llvm-readelf.com"
      - "E:/mingw64/bin/llvm-readelf.exe"
      - "E:/mingw64/bin/llvm-readelf"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/llvm-readelf.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/llvm-readelf.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/llvm-readelf"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/llvm-readelf.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/llvm-readelf.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/llvm-readelf"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/llvm-readelf.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/llvm-readelf.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/llvm-readelf"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/llvm-readelf.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/llvm-readelf.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/llvm-readelf"
      - "E:/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/llvm-readelf.com"
      - "E:/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/llvm-readelf.exe"
      - "E:/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/llvm-readelf"
      - "D:/audio/ffmpeg-2025-03-17-git-5b9356f18e-full_build/bin/llvm-readelf.com"
      - "D:/audio/ffmpeg-2025-03-17-git-5b9356f18e-full_build/bin/llvm-readelf.exe"
      - "D:/audio/ffmpeg-2025-03-17-git-5b9356f18e-full_build/bin/llvm-readelf"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/llvm-readelf.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/llvm-readelf.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/llvm-readelf"
      - "F:/cmake-4.0.2-windows-x86_64/bin/llvm-readelf.com"
      - "F:/cmake-4.0.2-windows-x86_64/bin/llvm-readelf.exe"
      - "F:/cmake-4.0.2-windows-x86_64/bin/llvm-readelf"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/llvm-readelf.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/llvm-readelf.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/llvm-readelf"
      - "C:/Users/<USER>/AppData/Local/Muse Hub/lib/llvm-readelf.com"
      - "C:/Users/<USER>/AppData/Local/Muse Hub/lib/llvm-readelf.exe"
      - "C:/Users/<USER>/AppData/Local/Muse Hub/lib/llvm-readelf"
      - "C:/Program Files/I-SIMPA/bin/llvm-readelf.com"
      - "C:/Program Files/I-SIMPA/bin/llvm-readelf.exe"
      - "C:/Program Files/I-SIMPA/bin/llvm-readelf"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/llvm-readelf.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/llvm-readelf.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/llvm-readelf"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/llvm-readelf.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/llvm-readelf.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/llvm-readelf"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/Linux/bin/ConnectionManagerExe/llvm-readelf.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/Linux/bin/ConnectionManagerExe/llvm-readelf.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/Linux/bin/ConnectionManagerExe/llvm-readelf"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/vcpkg/llvm-readelf.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/vcpkg/llvm-readelf.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/vcpkg/llvm-readelf"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/readelf.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/readelf.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/readelf"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/readelf.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/readelf.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/readelf"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/readelf.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/readelf.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/readelf"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/readelf.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/readelf.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/readelf"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/readelf.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/readelf.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/readelf"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/readelf.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/readelf.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/readelf"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/x64/readelf.com"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/x64/readelf.exe"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/x64/readelf"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/readelf.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/readelf.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/readelf"
      - "E:/Windows Kits/10/bin/10.0.26100.0/x64/readelf.com"
      - "E:/Windows Kits/10/bin/10.0.26100.0/x64/readelf.exe"
      - "E:/Windows Kits/10/bin/10.0.26100.0/x64/readelf"
      - "E:/Windows Kits/10/bin/x64/readelf.com"
      - "E:/Windows Kits/10/bin/x64/readelf.exe"
      - "E:/Windows Kits/10/bin/x64/readelf"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/readelf.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/readelf.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/readelf"
      - "C:/Windows/Microsoft.NET/Framework64/v4.0.30319/readelf.com"
      - "C:/Windows/Microsoft.NET/Framework64/v4.0.30319/readelf.exe"
      - "C:/Windows/Microsoft.NET/Framework64/v4.0.30319/readelf"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/readelf.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/readelf.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/readelf"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/Tools/readelf.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/Tools/readelf.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/Tools/readelf"
      - "C:/Program Files (x86)/VMware/VMware Workstation/bin/readelf.com"
      - "C:/Program Files (x86)/VMware/VMware Workstation/bin/readelf.exe"
      - "C:/Program Files (x86)/VMware/VMware Workstation/bin/readelf"
      - "C:/Windows/System32/readelf.com"
      - "C:/Windows/System32/readelf.exe"
      - "C:/Windows/System32/readelf"
      - "C:/Windows/readelf.com"
      - "C:/Windows/readelf.exe"
      - "C:/Windows/readelf"
      - "C:/Windows/System32/wbem/readelf.com"
      - "C:/Windows/System32/wbem/readelf.exe"
      - "C:/Windows/System32/wbem/readelf"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/readelf.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/readelf.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/readelf"
      - "C:/Windows/System32/OpenSSH/readelf.com"
      - "C:/Windows/System32/OpenSSH/readelf.exe"
      - "C:/Windows/System32/OpenSSH/readelf"
      - "D:/banizip/Bandizip/readelf.com"
      - "D:/banizip/Bandizip/readelf.exe"
      - "D:/banizip/Bandizip/readelf"
      - "E:/Bandizip/readelf.com"
      - "E:/Bandizip/readelf.exe"
      - "E:/Bandizip/readelf"
      - "E:/Git/cmd/readelf.com"
      - "E:/Git/cmd/readelf.exe"
      - "E:/Git/cmd/readelf"
      - "C:/Program Files/Docker/Docker/resources/bin/readelf.com"
      - "C:/Program Files/Docker/Docker/resources/bin/readelf.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/readelf"
      - "C:/ProgramData/chocolatey/bin/readelf.com"
      - "C:/ProgramData/chocolatey/bin/readelf.exe"
      - "C:/ProgramData/chocolatey/bin/readelf"
      - "C:/Program Files/dotnet/readelf.com"
      - "C:/Program Files/dotnet/readelf.exe"
      - "C:/Program Files/dotnet/readelf"
      - "E:/Windows Kits/10/Windows Performance Toolkit/readelf.com"
      - "E:/Windows Kits/10/Windows Performance Toolkit/readelf.exe"
      - "E:/Windows Kits/10/Windows Performance Toolkit/readelf"
      - "E:/mingw64/bin/readelf.com"
    found: "E:/mingw64/bin/readelf.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\VCPackages"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TestWindow"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TeamFoundation\\Team Explorer"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\bin\\Roslyn"
        - "C:\\Program Files (x86)\\Microsoft SDKs\\Windows\\v10.0A\\bin\\NETFX 4.8 Tools\\x64\\"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Team Tools\\DiagnosticsHub\\Collector"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin"
        - "E:\\Windows Kits\\10\\bin\\10.0.26100.0\\\\x64"
        - "E:\\Windows Kits\\10\\bin\\\\x64"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\\\MSBuild\\Current\\Bin\\amd64"
        - "C:\\Windows\\Microsoft.NET\\Framework64\\v4.0.30319"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\Tools\\"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "D:\\banizip\\Bandizip"
        - "E:\\Bandizip"
        - "E:\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\dotnet\\"
        - "E:\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "E:\\mingw64\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin"
        - "D:\\audio\\ffmpeg-2025-03-17-git-5b9356f18e-full_build\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "F:\\cmake-4.0.2-windows-x86_64\\bin"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\Hostx64\\x64"
        - "C:\\Users\\<USER>\\AppData\\Local\\Muse Hub\\lib"
        - "C:\\Program Files\\I-SIMPA\\bin"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\Ninja"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\Linux\\bin\\ConnectionManagerExe"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\vcpkg"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/qmltest"
  -
    kind: "find-v1"
    backtrace:
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_ADDR2LINE"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "llvm-addr2line"
      - "addr2line"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/x64/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/"
      - "E:/Windows Kits/10/bin/10.0.26100.0/x64/"
      - "E:/Windows Kits/10/bin/x64/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/"
      - "C:/Windows/Microsoft.NET/Framework64/v4.0.30319/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/Tools/"
      - "C:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "D:/banizip/Bandizip/"
      - "E:/Bandizip/"
      - "E:/Git/cmd/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/dotnet/"
      - "E:/Windows Kits/10/Windows Performance Toolkit/"
      - "E:/mingw64/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/"
      - "D:/audio/ffmpeg-2025-03-17-git-5b9356f18e-full_build/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "F:/cmake-4.0.2-windows-x86_64/bin/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Users/<USER>/AppData/Local/Muse Hub/lib/"
      - "C:/Program Files/I-SIMPA/bin/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/Linux/bin/ConnectionManagerExe/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/vcpkg/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/llvm-addr2line.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/llvm-addr2line.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/llvm-addr2line"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/llvm-addr2line.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/llvm-addr2line.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/llvm-addr2line"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/llvm-addr2line.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/llvm-addr2line.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/llvm-addr2line"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/llvm-addr2line.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/llvm-addr2line.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/llvm-addr2line"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/llvm-addr2line.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/llvm-addr2line.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/llvm-addr2line"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/llvm-addr2line.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/llvm-addr2line.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/llvm-addr2line"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/x64/llvm-addr2line.com"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/x64/llvm-addr2line.exe"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/x64/llvm-addr2line"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/llvm-addr2line.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/llvm-addr2line.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/llvm-addr2line"
      - "E:/Windows Kits/10/bin/10.0.26100.0/x64/llvm-addr2line.com"
      - "E:/Windows Kits/10/bin/10.0.26100.0/x64/llvm-addr2line.exe"
      - "E:/Windows Kits/10/bin/10.0.26100.0/x64/llvm-addr2line"
      - "E:/Windows Kits/10/bin/x64/llvm-addr2line.com"
      - "E:/Windows Kits/10/bin/x64/llvm-addr2line.exe"
      - "E:/Windows Kits/10/bin/x64/llvm-addr2line"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/llvm-addr2line.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/llvm-addr2line.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/llvm-addr2line"
      - "C:/Windows/Microsoft.NET/Framework64/v4.0.30319/llvm-addr2line.com"
      - "C:/Windows/Microsoft.NET/Framework64/v4.0.30319/llvm-addr2line.exe"
      - "C:/Windows/Microsoft.NET/Framework64/v4.0.30319/llvm-addr2line"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/llvm-addr2line.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/llvm-addr2line.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/llvm-addr2line"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/Tools/llvm-addr2line.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/Tools/llvm-addr2line.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/Tools/llvm-addr2line"
      - "C:/Program Files (x86)/VMware/VMware Workstation/bin/llvm-addr2line.com"
      - "C:/Program Files (x86)/VMware/VMware Workstation/bin/llvm-addr2line.exe"
      - "C:/Program Files (x86)/VMware/VMware Workstation/bin/llvm-addr2line"
      - "C:/Windows/System32/llvm-addr2line.com"
      - "C:/Windows/System32/llvm-addr2line.exe"
      - "C:/Windows/System32/llvm-addr2line"
      - "C:/Windows/llvm-addr2line.com"
      - "C:/Windows/llvm-addr2line.exe"
      - "C:/Windows/llvm-addr2line"
      - "C:/Windows/System32/wbem/llvm-addr2line.com"
      - "C:/Windows/System32/wbem/llvm-addr2line.exe"
      - "C:/Windows/System32/wbem/llvm-addr2line"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/llvm-addr2line.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/llvm-addr2line.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/llvm-addr2line"
      - "C:/Windows/System32/OpenSSH/llvm-addr2line.com"
      - "C:/Windows/System32/OpenSSH/llvm-addr2line.exe"
      - "C:/Windows/System32/OpenSSH/llvm-addr2line"
      - "D:/banizip/Bandizip/llvm-addr2line.com"
      - "D:/banizip/Bandizip/llvm-addr2line.exe"
      - "D:/banizip/Bandizip/llvm-addr2line"
      - "E:/Bandizip/llvm-addr2line.com"
      - "E:/Bandizip/llvm-addr2line.exe"
      - "E:/Bandizip/llvm-addr2line"
      - "E:/Git/cmd/llvm-addr2line.com"
      - "E:/Git/cmd/llvm-addr2line.exe"
      - "E:/Git/cmd/llvm-addr2line"
      - "C:/Program Files/Docker/Docker/resources/bin/llvm-addr2line.com"
      - "C:/Program Files/Docker/Docker/resources/bin/llvm-addr2line.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/llvm-addr2line"
      - "C:/ProgramData/chocolatey/bin/llvm-addr2line.com"
      - "C:/ProgramData/chocolatey/bin/llvm-addr2line.exe"
      - "C:/ProgramData/chocolatey/bin/llvm-addr2line"
      - "C:/Program Files/dotnet/llvm-addr2line.com"
      - "C:/Program Files/dotnet/llvm-addr2line.exe"
      - "C:/Program Files/dotnet/llvm-addr2line"
      - "E:/Windows Kits/10/Windows Performance Toolkit/llvm-addr2line.com"
      - "E:/Windows Kits/10/Windows Performance Toolkit/llvm-addr2line.exe"
      - "E:/Windows Kits/10/Windows Performance Toolkit/llvm-addr2line"
      - "E:/mingw64/bin/llvm-addr2line.com"
      - "E:/mingw64/bin/llvm-addr2line.exe"
      - "E:/mingw64/bin/llvm-addr2line"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/llvm-addr2line.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/llvm-addr2line.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/llvm-addr2line"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/llvm-addr2line.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/llvm-addr2line.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/llvm-addr2line"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/llvm-addr2line.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/llvm-addr2line.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/llvm-addr2line"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/llvm-addr2line.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/llvm-addr2line.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/llvm-addr2line"
      - "E:/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/llvm-addr2line.com"
      - "E:/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/llvm-addr2line.exe"
      - "E:/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/llvm-addr2line"
      - "D:/audio/ffmpeg-2025-03-17-git-5b9356f18e-full_build/bin/llvm-addr2line.com"
      - "D:/audio/ffmpeg-2025-03-17-git-5b9356f18e-full_build/bin/llvm-addr2line.exe"
      - "D:/audio/ffmpeg-2025-03-17-git-5b9356f18e-full_build/bin/llvm-addr2line"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/llvm-addr2line.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/llvm-addr2line.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/llvm-addr2line"
      - "F:/cmake-4.0.2-windows-x86_64/bin/llvm-addr2line.com"
      - "F:/cmake-4.0.2-windows-x86_64/bin/llvm-addr2line.exe"
      - "F:/cmake-4.0.2-windows-x86_64/bin/llvm-addr2line"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/llvm-addr2line.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/llvm-addr2line.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/llvm-addr2line"
      - "C:/Users/<USER>/AppData/Local/Muse Hub/lib/llvm-addr2line.com"
      - "C:/Users/<USER>/AppData/Local/Muse Hub/lib/llvm-addr2line.exe"
      - "C:/Users/<USER>/AppData/Local/Muse Hub/lib/llvm-addr2line"
      - "C:/Program Files/I-SIMPA/bin/llvm-addr2line.com"
      - "C:/Program Files/I-SIMPA/bin/llvm-addr2line.exe"
      - "C:/Program Files/I-SIMPA/bin/llvm-addr2line"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/llvm-addr2line.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/llvm-addr2line.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/llvm-addr2line"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/llvm-addr2line.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/llvm-addr2line.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/llvm-addr2line"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/Linux/bin/ConnectionManagerExe/llvm-addr2line.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/Linux/bin/ConnectionManagerExe/llvm-addr2line.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/Linux/bin/ConnectionManagerExe/llvm-addr2line"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/vcpkg/llvm-addr2line.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/vcpkg/llvm-addr2line.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/vcpkg/llvm-addr2line"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/addr2line.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/addr2line.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/addr2line"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/addr2line.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/addr2line.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/addr2line"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/addr2line.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/addr2line.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/addr2line"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/addr2line.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/addr2line.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/addr2line"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/addr2line.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/addr2line.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/addr2line"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/addr2line.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/addr2line.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/addr2line"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/x64/addr2line.com"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/x64/addr2line.exe"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/x64/addr2line"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/addr2line.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/addr2line.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/addr2line"
      - "E:/Windows Kits/10/bin/10.0.26100.0/x64/addr2line.com"
      - "E:/Windows Kits/10/bin/10.0.26100.0/x64/addr2line.exe"
      - "E:/Windows Kits/10/bin/10.0.26100.0/x64/addr2line"
      - "E:/Windows Kits/10/bin/x64/addr2line.com"
      - "E:/Windows Kits/10/bin/x64/addr2line.exe"
      - "E:/Windows Kits/10/bin/x64/addr2line"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/addr2line.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/addr2line.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/addr2line"
      - "C:/Windows/Microsoft.NET/Framework64/v4.0.30319/addr2line.com"
      - "C:/Windows/Microsoft.NET/Framework64/v4.0.30319/addr2line.exe"
      - "C:/Windows/Microsoft.NET/Framework64/v4.0.30319/addr2line"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/addr2line.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/addr2line.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/addr2line"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/Tools/addr2line.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/Tools/addr2line.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/Tools/addr2line"
      - "C:/Program Files (x86)/VMware/VMware Workstation/bin/addr2line.com"
      - "C:/Program Files (x86)/VMware/VMware Workstation/bin/addr2line.exe"
      - "C:/Program Files (x86)/VMware/VMware Workstation/bin/addr2line"
      - "C:/Windows/System32/addr2line.com"
      - "C:/Windows/System32/addr2line.exe"
      - "C:/Windows/System32/addr2line"
      - "C:/Windows/addr2line.com"
      - "C:/Windows/addr2line.exe"
      - "C:/Windows/addr2line"
      - "C:/Windows/System32/wbem/addr2line.com"
      - "C:/Windows/System32/wbem/addr2line.exe"
      - "C:/Windows/System32/wbem/addr2line"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/addr2line.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/addr2line.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/addr2line"
      - "C:/Windows/System32/OpenSSH/addr2line.com"
      - "C:/Windows/System32/OpenSSH/addr2line.exe"
      - "C:/Windows/System32/OpenSSH/addr2line"
      - "D:/banizip/Bandizip/addr2line.com"
      - "D:/banizip/Bandizip/addr2line.exe"
      - "D:/banizip/Bandizip/addr2line"
      - "E:/Bandizip/addr2line.com"
      - "E:/Bandizip/addr2line.exe"
      - "E:/Bandizip/addr2line"
      - "E:/Git/cmd/addr2line.com"
      - "E:/Git/cmd/addr2line.exe"
      - "E:/Git/cmd/addr2line"
      - "C:/Program Files/Docker/Docker/resources/bin/addr2line.com"
      - "C:/Program Files/Docker/Docker/resources/bin/addr2line.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/addr2line"
      - "C:/ProgramData/chocolatey/bin/addr2line.com"
      - "C:/ProgramData/chocolatey/bin/addr2line.exe"
      - "C:/ProgramData/chocolatey/bin/addr2line"
      - "C:/Program Files/dotnet/addr2line.com"
      - "C:/Program Files/dotnet/addr2line.exe"
      - "C:/Program Files/dotnet/addr2line"
      - "E:/Windows Kits/10/Windows Performance Toolkit/addr2line.com"
      - "E:/Windows Kits/10/Windows Performance Toolkit/addr2line.exe"
      - "E:/Windows Kits/10/Windows Performance Toolkit/addr2line"
      - "E:/mingw64/bin/addr2line.com"
    found: "E:/mingw64/bin/addr2line.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\VCPackages"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TestWindow"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TeamFoundation\\Team Explorer"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\bin\\Roslyn"
        - "C:\\Program Files (x86)\\Microsoft SDKs\\Windows\\v10.0A\\bin\\NETFX 4.8 Tools\\x64\\"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Team Tools\\DiagnosticsHub\\Collector"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin"
        - "E:\\Windows Kits\\10\\bin\\10.0.26100.0\\\\x64"
        - "E:\\Windows Kits\\10\\bin\\\\x64"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\\\MSBuild\\Current\\Bin\\amd64"
        - "C:\\Windows\\Microsoft.NET\\Framework64\\v4.0.30319"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\Tools\\"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "D:\\banizip\\Bandizip"
        - "E:\\Bandizip"
        - "E:\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\dotnet\\"
        - "E:\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "E:\\mingw64\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin"
        - "D:\\audio\\ffmpeg-2025-03-17-git-5b9356f18e-full_build\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "F:\\cmake-4.0.2-windows-x86_64\\bin"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\Hostx64\\x64"
        - "C:\\Users\\<USER>\\AppData\\Local\\Muse Hub\\lib"
        - "C:\\Program Files\\I-SIMPA\\bin"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\Ninja"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\Linux\\bin\\ConnectionManagerExe"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\vcpkg"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/qmltest"
  -
    kind: "find-v1"
    backtrace:
      - "E:/mingw64/share/cmake-4.1/Modules/Platform/Windows-Clang.cmake:201 (find_program)"
      - "E:/mingw64/share/cmake-4.1/Modules/Platform/Windows-Clang-CXX.cmake:1 (include)"
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeCXXInformation.cmake:48 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "__RC_COMPILER_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "rc"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/x64/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/"
      - "E:/Windows Kits/10/bin/10.0.26100.0/x64/"
      - "E:/Windows Kits/10/bin/x64/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/"
      - "C:/Windows/Microsoft.NET/Framework64/v4.0.30319/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/Tools/"
      - "C:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "D:/banizip/Bandizip/"
      - "E:/Bandizip/"
      - "E:/Git/cmd/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/dotnet/"
      - "E:/Windows Kits/10/Windows Performance Toolkit/"
      - "E:/mingw64/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/"
      - "D:/audio/ffmpeg-2025-03-17-git-5b9356f18e-full_build/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "F:/cmake-4.0.2-windows-x86_64/bin/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Users/<USER>/AppData/Local/Muse Hub/lib/"
      - "C:/Program Files/I-SIMPA/bin/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/Linux/bin/ConnectionManagerExe/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/vcpkg/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "E:/mingw64/bin/"
      - "E:/mingw64/sbin/"
      - "E:/mingw64/"
      - "C:/Program Files (x86)/qmltest/bin/"
      - "C:/Program Files (x86)/qmltest/sbin/"
      - "C:/Program Files (x86)/qmltest/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/rc.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/rc.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/rc"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/rc.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/rc.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/rc"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/rc.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/rc.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/rc"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/rc.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/rc.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/rc"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/rc.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/rc.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/rc"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/x64/rc.com"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/x64/rc.exe"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/x64/rc"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/rc.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/rc.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/rc"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/rc.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/rc.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/rc"
      - "E:/Windows Kits/10/bin/10.0.26100.0/x64/rc.com"
    found: "E:/Windows Kits/10/bin/10.0.26100.0/x64/rc.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\VCPackages"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TestWindow"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TeamFoundation\\Team Explorer"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\bin\\Roslyn"
        - "C:\\Program Files (x86)\\Microsoft SDKs\\Windows\\v10.0A\\bin\\NETFX 4.8 Tools\\x64\\"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Team Tools\\DiagnosticsHub\\Collector"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin"
        - "E:\\Windows Kits\\10\\bin\\10.0.26100.0\\\\x64"
        - "E:\\Windows Kits\\10\\bin\\\\x64"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\\\MSBuild\\Current\\Bin\\amd64"
        - "C:\\Windows\\Microsoft.NET\\Framework64\\v4.0.30319"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\Tools\\"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "D:\\banizip\\Bandizip"
        - "E:\\Bandizip"
        - "E:\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\dotnet\\"
        - "E:\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "E:\\mingw64\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin"
        - "D:\\audio\\ffmpeg-2025-03-17-git-5b9356f18e-full_build\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "F:\\cmake-4.0.2-windows-x86_64\\bin"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\Hostx64\\x64"
        - "C:\\Users\\<USER>\\AppData\\Local\\Muse Hub\\lib"
        - "C:\\Program Files\\I-SIMPA\\bin"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\Ninja"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\Linux\\bin\\ConnectionManagerExe"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\vcpkg"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/qmltest"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "E:/mingw64"
        - "C:/Program Files (x86)/qmltest"
  -
    kind: "try_compile-v1"
    backtrace:
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-4ur1gh"
      binary: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-4ur1gh"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/clang-scan-deps.exe"
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-4ur1gh'
        
        Run Build Command(s): "C:/PROGRA~1/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v cmTC_11990
        [1/2] "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe"   -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -MD -MT CMakeFiles/cmTC_11990.dir/CMakeCXXCompilerABI.cpp.obj -MF CMakeFiles\\cmTC_11990.dir\\CMakeCXXCompilerABI.cpp.obj.d -o CMakeFiles/cmTC_11990.dir/CMakeCXXCompilerABI.cpp.obj -c E:/mingw64/share/cmake-4.1/Modules/CMakeCXXCompilerABI.cpp
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe" -nostartfiles -nostdlib -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -v -Xlinker /subsystem:console -fuse-ld=lld-link CMakeFiles/cmTC_11990.dir/CMakeCXXCompilerABI.cpp.obj -o cmTC_11990.exe -Xlinker /MANIFEST:EMBED -Xlinker /implib:cmTC_11990.lib -Xlinker /pdb:cmTC_11990.pdb -Xlinker /version:0.0     && cd ."
        clang version 19.1.5
        Target: x86_64-pc-windows-msvc
        Thread model: posix
        InstalledDir: C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin
         "C:\\\\Program Files\\\\Microsoft Visual Studio\\\\2022\\\\Community\\\\VC\\\\Tools\\\\Llvm\\\\x64\\\\bin\\\\lld-link" -out:cmTC_11990.exe "-libpath:C:\\\\Program Files\\\\Microsoft Visual Studio\\\\2022\\\\Community\\\\VC\\\\Tools\\\\Llvm\\\\x64\\\\lib\\\\clang\\\\19\\\\lib\\\\windows" -nologo -debug /subsystem:console CMakeFiles/cmTC_11990.dir/CMakeCXXCompilerABI.cpp.obj /MANIFEST:EMBED /implib:cmTC_11990.lib /pdb:cmTC_11990.pdb /version:0.0\x0d
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/lld-link
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "E:/mingw64/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/lld-link" "-v"
      lld-link: warning: ignoring unknown argument '-v'
      lld-link: error: no input files
  -
    kind: "message-v1"
    backtrace:
      - "E:/mingw64/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/lld-link" "-V"
      lld-link: warning: ignoring unknown argument '-V'
      lld-link: error: no input files
  -
    kind: "message-v1"
    backtrace:
      - "E:/mingw64/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/lld-link" "--version"
      LLD 19.1.5
  -
    kind: "try_compile-v1"
    backtrace:
      - "E:/mingw64/share/cmake-4.1/Modules/Internal/FeatureTesting.cmake:34 (try_compile)"
      - "E:/mingw64/share/cmake-4.1/Modules/Internal/FeatureTesting.cmake:87 (_record_compiler_features)"
      - "E:/mingw64/share/cmake-4.1/Modules/Compiler/CMakeCommonCompilerMacros.cmake:124 (_record_compiler_features_cxx)"
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeDetermineCompilerSupport.cmake:70 (cmake_record_cxx_compile_features)"
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:83 (CMAKE_DETERMINE_COMPILER_SUPPORT)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compile features"
    directories:
      source: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-yid9yj"
      binary: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-yid9yj"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/clang-scan-deps.exe"
      CMAKE_CXX_FLAGS: ""
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_FEATURE_TEST"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-yid9yj'
        
        Run Build Command(s): "C:/PROGRA~1/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v cmTC_ec7b4
        [1/2] "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe"   -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd   -std=c++14 -MD -MT CMakeFiles/cmTC_ec7b4.dir/feature_tests.cxx.obj -MF CMakeFiles\\cmTC_ec7b4.dir\\feature_tests.cxx.obj.d -o CMakeFiles/cmTC_ec7b4.dir/feature_tests.cxx.obj -c C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-yid9yj/feature_tests.cxx
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe" -nostartfiles -nostdlib -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -Xlinker /subsystem:console -fuse-ld=lld-link CMakeFiles/cmTC_ec7b4.dir/feature_tests.cxx.obj -o cmTC_ec7b4.exe -Xlinker /MANIFEST:EMBED -Xlinker /implib:cmTC_ec7b4.lib -Xlinker /pdb:cmTC_ec7b4.pdb -Xlinker /version:0.0   -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -loldnames  && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "E:/mingw64/share/cmake-4.1/Modules/Internal/FeatureTesting.cmake:34 (try_compile)"
      - "E:/mingw64/share/cmake-4.1/Modules/Internal/FeatureTesting.cmake:87 (_record_compiler_features)"
      - "E:/mingw64/share/cmake-4.1/Modules/Compiler/CMakeCommonCompilerMacros.cmake:132 (_record_compiler_features_cxx)"
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeDetermineCompilerSupport.cmake:70 (cmake_record_cxx_compile_features)"
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:83 (CMAKE_DETERMINE_COMPILER_SUPPORT)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compile features"
    directories:
      source: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-0mmtg0"
      binary: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-0mmtg0"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/clang-scan-deps.exe"
      CMAKE_CXX_FLAGS: ""
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_FEATURE_TEST"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-0mmtg0'
        
        Run Build Command(s): "C:/PROGRA~1/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v cmTC_6bbfa
        [1/2] "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe"   -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd   -std=c++14 -MD -MT CMakeFiles/cmTC_6bbfa.dir/feature_tests.cxx.obj -MF CMakeFiles\\cmTC_6bbfa.dir\\feature_tests.cxx.obj.d -o CMakeFiles/cmTC_6bbfa.dir/feature_tests.cxx.obj -c C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-0mmtg0/feature_tests.cxx
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe" -nostartfiles -nostdlib -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -Xlinker /subsystem:console -fuse-ld=lld-link CMakeFiles/cmTC_6bbfa.dir/feature_tests.cxx.obj -o cmTC_6bbfa.exe -Xlinker /MANIFEST:EMBED -Xlinker /implib:cmTC_6bbfa.lib -Xlinker /pdb:cmTC_6bbfa.pdb -Xlinker /version:0.0   -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -loldnames  && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "find-v1"
    backtrace:
      - "E:/mingw64/share/cmake-4.1/Modules/Platform/Windows-Clang.cmake:201 (find_program)"
      - "E:/mingw64/share/cmake-4.1/Modules/Platform/Windows-Clang-CXX.cmake:1 (include)"
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeCXXInformation.cmake:48 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "__RC_COMPILER_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "rc"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/x64/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/"
      - "E:/Windows Kits/10/bin/10.0.26100.0/x64/"
      - "E:/Windows Kits/10/bin/x64/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/"
      - "C:/Windows/Microsoft.NET/Framework64/v4.0.30319/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/Tools/"
      - "C:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "D:/banizip/Bandizip/"
      - "E:/Bandizip/"
      - "E:/Git/cmd/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/dotnet/"
      - "E:/Windows Kits/10/Windows Performance Toolkit/"
      - "E:/mingw64/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/"
      - "D:/audio/ffmpeg-2025-03-17-git-5b9356f18e-full_build/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "F:/cmake-4.0.2-windows-x86_64/bin/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Users/<USER>/AppData/Local/Muse Hub/lib/"
      - "C:/Program Files/I-SIMPA/bin/"
      - "C:/Program Files/JetBrains/CLion 2025.2.1/bin/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/Linux/bin/ConnectionManagerExe/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/vcpkg/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "E:/mingw64/bin/"
      - "E:/mingw64/sbin/"
      - "E:/mingw64/"
      - "C:/Program Files (x86)/qmltest/bin/"
      - "C:/Program Files (x86)/qmltest/sbin/"
      - "C:/Program Files (x86)/qmltest/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/rc.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/rc.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/rc"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/rc.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/rc.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/rc"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/rc.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/rc.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/rc"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/rc.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/rc.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/rc"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/rc.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/rc.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/rc"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/x64/rc.com"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/x64/rc.exe"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/x64/rc"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/rc.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/rc.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/rc"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/rc.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/rc.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/rc"
      - "E:/Windows Kits/10/bin/10.0.26100.0/x64/rc.com"
    found: "E:/Windows Kits/10/bin/10.0.26100.0/x64/rc.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\VCPackages"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TestWindow"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TeamFoundation\\Team Explorer"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\bin\\Roslyn"
        - "C:\\Program Files (x86)\\Microsoft SDKs\\Windows\\v10.0A\\bin\\NETFX 4.8 Tools\\x64\\"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Team Tools\\DiagnosticsHub\\Collector"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin"
        - "E:\\Windows Kits\\10\\bin\\10.0.26100.0\\\\x64"
        - "E:\\Windows Kits\\10\\bin\\\\x64"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\\\MSBuild\\Current\\Bin\\amd64"
        - "C:\\Windows\\Microsoft.NET\\Framework64\\v4.0.30319"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\Tools\\"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "D:\\banizip\\Bandizip"
        - "E:\\Bandizip"
        - "E:\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\dotnet\\"
        - "E:\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "E:\\mingw64\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin"
        - "D:\\audio\\ffmpeg-2025-03-17-git-5b9356f18e-full_build\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "F:\\cmake-4.0.2-windows-x86_64\\bin"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\Hostx64\\x64"
        - "C:\\Users\\<USER>\\AppData\\Local\\Muse Hub\\lib"
        - "C:\\Program Files\\I-SIMPA\\bin"
        - "C:\\Program Files\\JetBrains\\CLion 2025.2.1\\bin"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\Ninja"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\Linux\\bin\\ConnectionManagerExe"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\vcpkg"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/qmltest"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "E:/mingw64"
        - "C:/Program Files (x86)/qmltest"
...

---
events:
  -
    kind: "find-v1"
    backtrace:
      - "E:/mingw64/share/cmake-4.1/Modules/Platform/Windows-Clang.cmake:201 (find_program)"
      - "E:/mingw64/share/cmake-4.1/Modules/Platform/Windows-Clang-CXX.cmake:1 (include)"
      - "E:/mingw64/share/cmake-4.1/Modules/CMakeCXXInformation.cmake:48 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "__RC_COMPILER_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "rc"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/x64/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/"
      - "E:/Windows Kits/10/bin/10.0.26100.0/x64/"
      - "E:/Windows Kits/10/bin/x64/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/"
      - "C:/Windows/Microsoft.NET/Framework64/v4.0.30319/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/Tools/"
      - "C:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "D:/banizip/Bandizip/"
      - "E:/Bandizip/"
      - "E:/Git/cmd/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/dotnet/"
      - "E:/Windows Kits/10/Windows Performance Toolkit/"
      - "E:/mingw64/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/"
      - "D:/audio/ffmpeg-2025-03-17-git-5b9356f18e-full_build/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "F:/cmake-4.0.2-windows-x86_64/bin/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Users/<USER>/AppData/Local/Muse Hub/lib/"
      - "C:/Program Files/I-SIMPA/bin/"
      - "C:/Program Files/JetBrains/CLion 2025.2.1/bin/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/Linux/bin/ConnectionManagerExe/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/vcpkg/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "E:/mingw64/bin/"
      - "E:/mingw64/sbin/"
      - "E:/mingw64/"
      - "C:/Program Files (x86)/qmltest/bin/"
      - "C:/Program Files (x86)/qmltest/sbin/"
      - "C:/Program Files (x86)/qmltest/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/rc.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/rc.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/rc"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/rc.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/rc.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/rc"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/rc.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/rc.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/rc"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/rc.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/rc.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/rc"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/rc.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/rc.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/rc"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/x64/rc.com"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/x64/rc.exe"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/x64/rc"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/rc.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/rc.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/rc"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/rc.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/rc.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/rc"
      - "E:/Windows Kits/10/bin/10.0.26100.0/x64/rc.com"
    found: "E:/Windows Kits/10/bin/10.0.26100.0/x64/rc.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\VCPackages"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TestWindow"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TeamFoundation\\Team Explorer"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\bin\\Roslyn"
        - "C:\\Program Files (x86)\\Microsoft SDKs\\Windows\\v10.0A\\bin\\NETFX 4.8 Tools\\x64\\"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Team Tools\\DiagnosticsHub\\Collector"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin"
        - "E:\\Windows Kits\\10\\bin\\10.0.26100.0\\\\x64"
        - "E:\\Windows Kits\\10\\bin\\\\x64"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\\\MSBuild\\Current\\Bin\\amd64"
        - "C:\\Windows\\Microsoft.NET\\Framework64\\v4.0.30319"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\Tools\\"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "D:\\banizip\\Bandizip"
        - "E:\\Bandizip"
        - "E:\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\dotnet\\"
        - "E:\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "E:\\mingw64\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin"
        - "D:\\audio\\ffmpeg-2025-03-17-git-5b9356f18e-full_build\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "F:\\cmake-4.0.2-windows-x86_64\\bin"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\Hostx64\\x64"
        - "C:\\Users\\<USER>\\AppData\\Local\\Muse Hub\\lib"
        - "C:\\Program Files\\I-SIMPA\\bin"
        - "C:\\Program Files\\JetBrains\\CLion 2025.2.1\\bin"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\Ninja"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\Linux\\bin\\ConnectionManagerExe"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\vcpkg"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/qmltest"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "E:/mingw64"
        - "C:/Program Files (x86)/qmltest"
...
