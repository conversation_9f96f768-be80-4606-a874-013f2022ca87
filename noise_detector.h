#ifndef NOISE_DETECTOR_H
#define NOISE_DETECTOR_H

#include <vector>
#include <string>
#include <map>
#include <memory>

/**
 * @brief 杂音检测器类
 * 
 * 基于分析人声、多频音和静音时的日志数据，设计的杂音检测算法
 * 能够检测人声和多频音，但不会误报静音时的内容
 */
class NoiseDetector {
public:
    /**
     * @brief 检测结果枚举
     */
    enum class DetectionResult {
        SILENCE,        // 静音
        VOICE,          // 人声
        MULTI_TONE,     // 多频音
        UNKNOWN_NOISE   // 未知杂音
    };

    /**
     * @brief 频谱数据结构
     */
    struct FrequencyData {
        int frequency;      // 频率 (Hz)
        double energy;      // 能量值
    };

    /**
     * @brief 检测参数配置
     */
    struct DetectionConfig {
        // 能量阈值
        double voiceEnergyThreshold = 50.0;        // 人声能量阈值
        double multiToneEnergyThreshold = 100.0;   // 多频音能量阈值
        double silenceEnergyThreshold = 10.0;      // 静音能量阈值
        
        // 频率范围
        int voiceFreqMin = 125;     // 人声频率最小值 (Hz)
        int voiceFreqMax = 4000;    // 人声频率最大值 (Hz)
        
        // 峰值检测
        int minPeakCount = 2;       // 最小峰值数量
        double peakRatio = 3.0;     // 峰值与平均值的比例
        
        // 多频音特征
        double multiToneSpread = 0.8;   // 多频音频谱分布系数
        int multiToneMinFreqs = 3;      // 多频音最少频率成分
        
        // 连续性检测
        int continuityFrames = 3;       // 连续帧数要求
    };

private:
    DetectionConfig config_;
    std::vector<DetectionResult> recentResults_;
    int maxHistorySize_ = 10;

    /**
     * @brief 解析nearendFFT数据
     */
    std::vector<FrequencyData> parseFFTData(const std::string& fftString);

    /**
     * @brief 计算总能量
     */
    double calculateTotalEnergy(const std::vector<FrequencyData>& fftData);

    /**
     * @brief 检测频谱峰值
     */
    std::vector<FrequencyData> detectPeaks(const std::vector<FrequencyData>& fftData);

    /**
     * @brief 分析人声特征
     */
    bool analyzeVoiceCharacteristics(const std::vector<FrequencyData>& fftData, 
                                   const std::vector<FrequencyData>& peaks);

    /**
     * @brief 分析多频音特征
     */
    bool analyzeMultiToneCharacteristics(const std::vector<FrequencyData>& fftData,
                                       const std::vector<FrequencyData>& peaks);

    /**
     * @brief 检查是否为静音
     */
    bool isSilence(const std::vector<FrequencyData>& fftData, double totalEnergy);

    /**
     * @brief 应用连续性过滤
     */
    DetectionResult applyContinuityFilter(DetectionResult currentResult);

public:
    /**
     * @brief 构造函数
     */
    NoiseDetector(const DetectionConfig& config = DetectionConfig());

    /**
     * @brief 设置检测配置
     */
    void setConfig(const DetectionConfig& config);

    /**
     * @brief 获取当前配置
     */
    const DetectionConfig& getConfig() const;

    /**
     * @brief 主检测函数
     * @param nearendFFT nearendFFT数据字符串
     * @return 检测结果
     */
    DetectionResult detect(const std::string& nearendFFT);

    /**
     * @brief 批量检测
     * @param fftDataList 多帧FFT数据
     * @return 检测结果列表
     */
    std::vector<DetectionResult> detectBatch(const std::vector<std::string>& fftDataList);

    /**
     * @brief 重置检测器状态
     */
    void reset();

    /**
     * @brief 获取检测结果的字符串表示
     */
    static std::string resultToString(DetectionResult result);

    /**
     * @brief 获取检测统计信息
     */
    std::map<std::string, int> getStatistics() const;
};

#endif // NOISE_DETECTOR_H
