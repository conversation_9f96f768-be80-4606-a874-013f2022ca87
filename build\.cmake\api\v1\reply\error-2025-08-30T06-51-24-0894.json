{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "E:/mingw64/bin/cmake.exe", "cpack": "E:/mingw64/bin/cpack.exe", "ctest": "E:/mingw64/bin/ctest.exe", "root": "E:/mingw64/share/cmake-4.1"}, "version": {"isDirty": false, "major": 4, "minor": 1, "patch": 0, "string": "4.1.0", "suffix": ""}}, "objects": [], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"error": "no buildsystem generated"}, {"error": "no buildsystem generated"}, {"error": "no buildsystem generated"}, {"error": "no buildsystem generated"}]}}}}