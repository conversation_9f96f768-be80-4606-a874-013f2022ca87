#include "noise_detector.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <vector>
#include <string>
#include <chrono>

/**
 * @brief 从文件读取测试数据
 */
std::vector<std::string> readTestData(const std::string& filename) {
    std::vector<std::string> fftLines;
    std::ifstream file(filename);
    std::string line;
    
    while (std::getline(file, line)) {
        // 查找包含nearendFFT的行
        if (line.find("nearendFFT:") != std::string::npos) {
            // 提取FFT数据部分
            size_t pos = line.find("nearendFFT:");
            if (pos != std::string::npos) {
                std::string fftData = line.substr(pos + 11); // 跳过"nearendFFT:"
                fftLines.push_back(fftData);
            }
        }
    }
    
    return fftLines;
}

/**
 * @brief 测试单个文件
 */
void testFile(const std::string& filename, const std::string& expectedType, NoiseDetector& detector) {
    std::cout << "\n=== 测试文件: " << filename << " (期望类型: " << expectedType << ") ===" << std::endl;
    
    std::vector<std::string> fftData = readTestData(filename);
    
    if (fftData.empty()) {
        std::cout << "警告: 未找到FFT数据" << std::endl;
        return;
    }
    
    std::map<std::string, int> resultCount;
    
    // 逐帧检测
    for (size_t i = 0; i < fftData.size() && i < 10; ++i) {  // 只测试前10帧
        NoiseDetector::DetectionResult result = detector.detect(fftData[i]);
        std::string resultStr = NoiseDetector::resultToString(result);
        resultCount[resultStr]++;
        
        std::cout << "帧 " << (i+1) << ": " << resultStr << std::endl;
    }
    
    // 统计结果
    std::cout << "\n检测结果统计:" << std::endl;
    for (const auto& pair : resultCount) {
        std::cout << pair.first << ": " << pair.second << " 次" << std::endl;
    }
    
    // 找到最频繁的结果
    std::string mostFrequent;
    int maxCount = 0;
    for (const auto& pair : resultCount) {
        if (pair.second > maxCount) {
            maxCount = pair.second;
            mostFrequent = pair.first;
        }
    }
    
    std::cout << "主要检测结果: " << mostFrequent << std::endl;
    
    // 重置检测器状态
    detector.reset();
}

/**
 * @brief 测试配置调优
 */
void testConfigTuning() {
    std::cout << "\n=== 配置参数调优测试 ===" << std::endl;
    
    NoiseDetector::DetectionConfig config;
    
    // 测试不同的阈值设置
    std::vector<double> voiceThresholds = {30.0, 50.0, 70.0};
    std::vector<double> multiToneThresholds = {80.0, 100.0, 150.0};
    
    for (double voiceThresh : voiceThresholds) {
        for (double multiToneThresh : multiToneThresholds) {
            config.voiceEnergyThreshold = voiceThresh;
            config.multiToneEnergyThreshold = multiToneThresh;
            
            NoiseDetector detector(config);
            
            std::cout << "\n配置: 人声阈值=" << voiceThresh 
                      << ", 多频音阈值=" << multiToneThresh << std::endl;
            
            // 这里可以添加具体的测试逻辑
            // 由于篇幅限制，这里只是展示框架
        }
    }
}

/**
 * @brief 性能测试
 */
void performanceTest() {
    std::cout << "\n=== 性能测试 ===" << std::endl;
    
    NoiseDetector detector;
    
    // 创建测试数据
    std::string testFFT = "[0HZ 72.696335][62HZ 38.357399][125HZ 1.847786][187HZ 9.510880][250HZ 14.777546]";
    
    auto start = std::chrono::high_resolution_clock::now();
    
    // 执行1000次检测
    for (int i = 0; i < 1000; ++i) {
        detector.detect(testFFT);
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    
    std::cout << "1000次检测耗时: " << duration.count() << " 微秒" << std::endl;
    std::cout << "平均每次检测: " << duration.count() / 1000.0 << " 微秒" << std::endl;
}

int main() {
    std::cout << "杂音检测器测试程序" << std::endl;
    std::cout << "==================" << std::endl;
    
    // 创建检测器实例
    NoiseDetector detector;
    
    // 显示当前配置
    const auto& config = detector.getConfig();
    std::cout << "\n当前配置:" << std::endl;
    std::cout << "人声能量阈值: " << config.voiceEnergyThreshold << std::endl;
    std::cout << "多频音能量阈值: " << config.multiToneEnergyThreshold << std::endl;
    std::cout << "静音能量阈值: " << config.silenceEnergyThreshold << std::endl;
    std::cout << "最小峰值数量: " << config.minPeakCount << std::endl;
    
    // 测试各种类型的数据
    testFile("人声日志输出.txt", "VOICE", detector);
    testFile("日志输出.txt", "MULTI_TONE", detector);
    testFile("静音时.txt", "SILENCE", detector);
    
    // 配置调优测试
    testConfigTuning();
    
    // 性能测试
    performanceTest();
    
    std::cout << "\n测试完成!" << std::endl;
    
    return 0;
}
