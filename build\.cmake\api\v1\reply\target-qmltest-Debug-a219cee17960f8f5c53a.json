{"artifacts": [{"path": "qmltest.exe"}, {"path": "qmltest.pdb"}], "backtrace": 4, "backtraceGraph": {"commands": ["add_executable", "_qt_internal_create_executable", "qt6_add_executable", "qt_add_executable", "target_link_libraries", "set_target_properties", "include", "find_package", "find_dependency", "_qt_internal_find_qt_dependencies", "qt6_add_qml_module", "qt_add_qml_module", "add_dependencies", "_qt_internal_scan_qml_imports", "_qt_internal_generate_deploy_qml_imports_script", "cmake_language", "_qt_internal_finalize_executable", "qt6_finalize_target", "_qt_internal_qml_copy_files_to_build_dir", "qt6_target_qml_sources", "target_include_directories", "_qt_internal_qml_type_registration", "target_sources", "qt6_extract_metatypes", "__qt_propagate_generated_resource", "_qt_internal_process_resource", "qt6_add_resources", "_qt_internal_target_enable_qmlcachegen", "_qt_internal_expose_deferred_files_to_ide"], "files": ["C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake", "CMakeLists.txt", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Quick/Qt6QuickTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Quick/Qt6QuickConfig.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Config.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6QmlMeta/Qt6QmlMetaTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6QmlMeta/Qt6QmlMetaConfig.cmake", "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Quick/Qt6QuickDependencies.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Qml/Qt6QmlMacros.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Qml/Qt6QmlTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Qml/Qt6QmlConfig.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6NetworkTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6NetworkConfig.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Qml/Qt6QmlDependencies.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfig.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake:787:EVAL"], "nodes": [{"file": 1}, {"command": 3, "file": 1, "line": 12, "parent": 0}, {"command": 2, "file": 0, "line": 935, "parent": 1}, {"command": 1, "file": 0, "line": 639, "parent": 2}, {"command": 0, "file": 0, "line": 690, "parent": 3}, {"command": 4, "file": 1, "line": 30, "parent": 0}, {"command": 7, "file": 1, "line": 8, "parent": 0}, {"file": 4, "parent": 6}, {"command": 7, "file": 4, "line": 218, "parent": 7}, {"file": 3, "parent": 8}, {"command": 6, "file": 3, "line": 55, "parent": 9}, {"file": 2, "parent": 10}, {"command": 5, "file": 2, "line": 61, "parent": 11}, {"command": 6, "file": 3, "line": 43, "parent": 9}, {"file": 9, "parent": 13}, {"command": 9, "file": 9, "line": 45, "parent": 14}, {"command": 8, "file": 8, "line": 137, "parent": 15}, {"command": 7, "file": 7, "line": 78, "parent": 16}, {"file": 6, "parent": 17}, {"command": 6, "file": 6, "line": 55, "parent": 18}, {"file": 5, "parent": 19}, {"command": 5, "file": 5, "line": 61, "parent": 20}, {"command": 11, "file": 1, "line": 16, "parent": 0}, {"command": 10, "file": 10, "line": 1252, "parent": 22}, {"command": 4, "file": 10, "line": 596, "parent": 23}, {"command": 8, "file": 8, "line": 137, "parent": 15}, {"command": 7, "file": 7, "line": 78, "parent": 25}, {"file": 12, "parent": 26}, {"command": 6, "file": 12, "line": 58, "parent": 27}, {"file": 11, "parent": 28}, {"command": 5, "file": 11, "line": 61, "parent": 29}, {"command": 6, "file": 12, "line": 46, "parent": 27}, {"file": 15, "parent": 31}, {"command": 9, "file": 15, "line": 45, "parent": 32}, {"command": 8, "file": 8, "line": 137, "parent": 33}, {"command": 7, "file": 7, "line": 78, "parent": 34}, {"file": 14, "parent": 35}, {"command": 6, "file": 14, "line": 55, "parent": 36}, {"file": 13, "parent": 37}, {"command": 5, "file": 13, "line": 61, "parent": 38}, {"command": 4, "file": 0, "line": 640, "parent": 2}, {"command": 7, "file": 4, "line": 218, "parent": 7}, {"file": 17, "parent": 41}, {"command": 6, "file": 17, "line": 57, "parent": 42}, {"file": 16, "parent": 43}, {"command": 5, "file": 16, "line": 61, "parent": 44}, {"command": 6, "file": 17, "line": 45, "parent": 42}, {"file": 20, "parent": 46}, {"command": 9, "file": 20, "line": 46, "parent": 47}, {"command": 8, "file": 8, "line": 137, "parent": 48}, {"command": 7, "file": 7, "line": 78, "parent": 49}, {"file": 19, "parent": 50}, {"command": 6, "file": 19, "line": 55, "parent": 51}, {"file": 18, "parent": 52}, {"command": 5, "file": 18, "line": 61, "parent": 53}, {"command": 8, "file": 8, "line": 137, "parent": 15}, {"command": 7, "file": 7, "line": 78, "parent": 55}, {"file": 22, "parent": 56}, {"command": 6, "file": 22, "line": 55, "parent": 57}, {"file": 21, "parent": 58}, {"command": 5, "file": 21, "line": 61, "parent": 59}, {"file": 1, "line": -1, "parent": 0}, {"command": 17, "file": 23, "line": 1, "parent": 61}, {"command": 16, "file": 0, "line": 818, "parent": 62}, {"command": 15, "file": 0, "line": 740, "parent": 63}, {"command": 14, "file": 0, "line": 740, "parent": 64}, {"command": 13, "file": 10, "line": 4401, "parent": 65}, {"command": 12, "file": 10, "line": 4176, "parent": 66}, {"command": 19, "file": 10, "line": 916, "parent": 23}, {"command": 18, "file": 10, "line": 3503, "parent": 68}, {"command": 12, "file": 10, "line": 2860, "parent": 69}, {"command": 18, "file": 10, "line": 3497, "parent": 68}, {"command": 12, "file": 10, "line": 2860, "parent": 71}, {"command": 21, "file": 10, "line": 796, "parent": 23}, {"command": 20, "file": 10, "line": 3879, "parent": 73}, {"command": 20, "file": 10, "line": 3904, "parent": 73}, {"command": 23, "file": 10, "line": 3704, "parent": 73}, {"command": 22, "file": 0, "line": 1486, "parent": 76}, {"command": 22, "file": 10, "line": 3870, "parent": 73}, {"command": 26, "file": 10, "line": 850, "parent": 23}, {"command": 25, "file": 0, "line": 401, "parent": 79}, {"command": 24, "file": 0, "line": 2528, "parent": 80}, {"command": 22, "file": 0, "line": 2072, "parent": 81}, {"command": 22, "file": 10, "line": 3205, "parent": 68}, {"command": 27, "file": 10, "line": 3342, "parent": 68}, {"command": 22, "file": 10, "line": 1696, "parent": 84}, {"command": 22, "file": 10, "line": 3398, "parent": 68}, {"command": 26, "file": 10, "line": 3519, "parent": 68}, {"command": 25, "file": 0, "line": 401, "parent": 87}, {"command": 24, "file": 0, "line": 2528, "parent": 88}, {"command": 22, "file": 0, "line": 2072, "parent": 89}, {"command": 28, "file": 0, "line": 812, "parent": 62}, {"command": 22, "file": 0, "line": 2197, "parent": 91}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-O0 -g -Xclang -gcodeview -std=gnu++17 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd"}], "defines": [{"backtrace": 40, "define": "MINGW_HAS_SECURE_API=1"}, {"backtrace": 40, "define": "QT_CORE_LIB"}, {"backtrace": 5, "define": "QT_GUI_LIB"}, {"backtrace": 40, "define": "QT_NEEDS_QMAIN"}, {"backtrace": 24, "define": "QT_NETWORK_LIB"}, {"backtrace": 5, "define": "QT_OPENGL_LIB"}, {"backtrace": 24, "define": "QT_QMLINTEGRATION_LIB"}, {"backtrace": 5, "define": "QT_QMLMETA_LIB"}, {"backtrace": 5, "define": "QT_QMLMODELS_LIB"}, {"backtrace": 5, "define": "QT_QMLWORKERSCRIPT_LIB"}, {"backtrace": 24, "define": "QT_QML_LIB"}, {"backtrace": 5, "define": "QT_QUICK_LIB"}, {"backtrace": 40, "define": "UNICODE"}, {"backtrace": 40, "define": "WIN32"}, {"backtrace": 40, "define": "WIN64"}, {"backtrace": 40, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 40, "define": "_UNICODE"}, {"backtrace": 40, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "C:/Users/<USER>/Documents/augment-projects/qmltest/build/qmltest_autogen/include"}, {"backtrace": 74, "path": "C:/Users/<USER>/Documents/augment-projects/qmltest"}, {"backtrace": 75, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQml/6.9.1"}, {"backtrace": 75, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQml/6.9.1/QtQml"}, {"backtrace": 75, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtCore/6.9.1"}, {"backtrace": 75, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtCore/6.9.1/QtCore"}, {"backtrace": 75, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtCore"}, {"backtrace": 75, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include"}, {"backtrace": 75, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++"}, {"backtrace": 75, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQml"}, {"backtrace": 75, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQmlIntegration"}, {"backtrace": 75, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtNetwork"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQuick"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtGui"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQmlMeta"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQmlModels"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQmlWorkerScript"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtOpenGL"}], "language": "CXX", "languageStandard": {"backtraces": [40, 40], "standard": "17"}, "sourceIndexes": [0, 1, 3, 4, 6, 7, 8]}], "dependencies": [{"backtrace": 67, "id": "qmltest_qmlimportscan::@6890427a1f51a3e7e1df"}, {"backtrace": 70, "id": "qmltest_copy_res::@6890427a1f51a3e7e1df"}, {"id": "qmltest_autogen_timestamp_deps::@6890427a1f51a3e7e1df"}, {"backtrace": 0, "id": "qmltest_autogen::@6890427a1f51a3e7e1df"}, {"backtrace": 72, "id": "qmltest_copy_qml::@6890427a1f51a3e7e1df"}], "id": "qmltest::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd", "role": "flags"}, {"fragment": "-Xlinker /subsystem:windows", "role": "flags"}, {"fragment": "-fuse-ld=lld-link", "role": "flags"}, {"backtrace": 5, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Quick.a", "role": "libraries"}, {"backtrace": 12, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6QmlMeta.a", "role": "libraries"}, {"backtrace": 21, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6QmlWorkerScript.a", "role": "libraries"}, {"backtrace": 12, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6QmlModels.a", "role": "libraries"}, {"backtrace": 24, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Qml.a", "role": "libraries"}, {"backtrace": 30, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Network.a", "role": "libraries"}, {"backtrace": 39, "fragment": "-lws2_32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6OpenGL.a", "role": "libraries"}, {"backtrace": 12, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Gui.a", "role": "libraries"}, {"backtrace": 40, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Core.a", "role": "libraries"}, {"backtrace": 45, "fragment": "-lmpr.lib", "role": "libraries"}, {"backtrace": 45, "fragment": "-luserenv.lib", "role": "libraries"}, {"backtrace": 54, "fragment": "-lmingw32.lib", "role": "libraries"}, {"backtrace": 54, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6EntryPoint.a", "role": "libraries"}, {"backtrace": 30, "fragment": "-lshell32.lib", "role": "libraries"}, {"backtrace": 60, "fragment": "-ld3d11.lib", "role": "libraries"}, {"backtrace": 60, "fragment": "-ldxgi.lib", "role": "libraries"}, {"backtrace": 60, "fragment": "-ldxguid.lib", "role": "libraries"}, {"backtrace": 60, "fragment": "-ld3d12.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "-luser32.lib", "role": "libraries"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -loldnames", "role": "libraries"}], "language": "CXX"}, "name": "qmltest", "nameOnDisk": "qmltest.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files\\Generated", "sourceIndexes": [0, 2, 3, 4, 6, 7, 8, 9, 10, 11, 12, 21, 22, 23, 24, 25, 26, 27, 28, 29]}, {"name": "Source Files", "sourceIndexes": [1, 5]}, {"name": "CMake Rules", "sourceIndexes": [13, 14, 15, 16, 17, 18, 19, 20]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/qmltest_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 1}, {"backtrace": 77, "isGenerated": true, "path": "build/meta_types/qt6qmltest_debug_metatypes.json.gen", "sourceGroupIndex": 0}, {"backtrace": 78, "compileGroupIndex": 0, "isGenerated": true, "path": "build/qmltest_qmltyperegistrations.cpp", "sourceGroupIndex": 0}, {"backtrace": 82, "compileGroupIndex": 0, "isGenerated": true, "path": "build/.qt/rcc/qrc_qmake_qmltest.cpp", "sourceGroupIndex": 0}, {"backtrace": 83, "path": "main.qml", "sourceGroupIndex": 1}, {"backtrace": 85, "compileGroupIndex": 0, "isGenerated": true, "path": "build/.rcc/qmlcache/qmltest_qmlcache_loader.cpp", "sourceGroupIndex": 0}, {"backtrace": 86, "compileGroupIndex": 0, "isGenerated": true, "path": "build/.rcc/qmlcache/qmltest_main_qml.cpp", "sourceGroupIndex": 0}, {"backtrace": 90, "compileGroupIndex": 0, "isGenerated": true, "path": "build/.qt/rcc/qrc_qmltest_raw_qml_0.cpp", "sourceGroupIndex": 0}, {"backtrace": 92, "isGenerated": true, "path": "build/.qt/rcc/qmake_qmltest.qrc", "sourceGroupIndex": 0}, {"backtrace": 92, "isGenerated": true, "path": "build/qmltest/qmldir", "sourceGroupIndex": 0}, {"backtrace": 92, "isGenerated": true, "path": "build/.qt/rcc/qmltest_raw_qml_0.qrc", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/qmltest_autogen/timestamp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/meta_types/qt6qmltest_debug_metatypes.json.gen.rule", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "build/qmltest_qmltyperegistrations.cpp.rule", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "build/.qt/rcc/qrc_qmake_qmltest.cpp.rule", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "build/.rcc/qmlcache/qmltest_qmlcache_loader.cpp.rule", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "build/.rcc/qmlcache/qmltest_main_qml.cpp.rule", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "build/.qt/rcc/qrc_qmltest_raw_qml_0.cpp.rule", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "build/qmltest_autogen/timestamp.rule", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "build/meta_types/qmltest_json_file_list.txt.rule", "sourceGroupIndex": 2}, {"backtrace": 40, "path": "C:/Qt/6.9.1/mingw_64/metatypes/qt6core_relwithdebinfo_metatypes.json", "sourceGroupIndex": 0}, {"backtrace": 24, "path": "C:/Qt/6.9.1/mingw_64/metatypes/qt6qml_relwithdebinfo_metatypes.json", "sourceGroupIndex": 0}, {"backtrace": 24, "path": "C:/Qt/6.9.1/mingw_64/metatypes/qt6network_relwithdebinfo_metatypes.json", "sourceGroupIndex": 0}, {"backtrace": 5, "path": "C:/Qt/6.9.1/mingw_64/metatypes/qt6quick_relwithdebinfo_metatypes.json", "sourceGroupIndex": 0}, {"backtrace": 5, "path": "C:/Qt/6.9.1/mingw_64/metatypes/qt6gui_relwithdebinfo_metatypes.json", "sourceGroupIndex": 0}, {"backtrace": 5, "path": "C:/Qt/6.9.1/mingw_64/metatypes/qt6qmlmeta_relwithdebinfo_metatypes.json", "sourceGroupIndex": 0}, {"backtrace": 5, "path": "C:/Qt/6.9.1/mingw_64/metatypes/qt6qmlmodels_relwithdebinfo_metatypes.json", "sourceGroupIndex": 0}, {"backtrace": 5, "path": "C:/Qt/6.9.1/mingw_64/metatypes/qt6qmlworkerscript_relwithdebinfo_metatypes.json", "sourceGroupIndex": 0}, {"backtrace": 5, "path": "C:/Qt/6.9.1/mingw_64/metatypes/qt6opengl_relwithdebinfo_metatypes.json", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}