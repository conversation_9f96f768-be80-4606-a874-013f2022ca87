#include <deque>
#include <iostream>
#define SAMPLE_SIZE 160
struct AnalysisUnit{
    short farendPcm[SAMPLE_SIZE];
    short nearendPcm[SAMPLE_SIZE];
};
//Frame是自定义音频帧结构,由于该帧采样率可能不符合16000要求，需要进行重采样
//而进行了重采样后，数据的大小就会发送变化，可能一帧已经不止160的采样点了(注：采样间隔为10ms)
//rtc开头的函数是aec算法相关函数，用于填充内部缓冲区
void readAECPcm(const std::shared_ptr<Frame> &frame){
    if(frame->getSampleRate() != 16000){
        resample(frame->data, frame->getSampleRate(), 16000);
    }
    if(frame->type == FrameType::NearendPcm){
        int pos = 0;
        int size = frame->data.size();
        while(frame->data.size() > SAMPLE_SIZE * sizeof(short)){
            rtc_aec_process(frame->data + pos, SAMPLE_SIZE * sizeof(short));
            pos += SAMPLE_SIZE * sizeof(short);
            size -= SAMPLE_SIZE * sizeof(short);
        }

    }else if(frame->type == FrameType::FarendPcm){
        int pos = 0;
        int size = frame->data.size();
        while(size > SAMPLE_SIZE * sizeof(short)){
            rtc_aec_fill_farend(frame->data + pos, SAMPLE_SIZE * sizeof(short));
            pos += SAMPLE_SIZE * sizeof(short);
            size -= SAMPLE_SIZE * sizeof(short);
        }
    }
}


int main() {

}