import QtQuick 2.12
import QtQuick.Window 2.12
import QtQuick.Controls 2.12
import QtQuick.Layouts 1.12
import QtQml.Models 2.12

Rectangle {
    anchors.fill: parent
    color: "white"

    // ???????ScrollView??Flickable????????
    ScrollView {
        id: scrollView
        anchors.fill: parent
        anchors.margins: 20

        // ????1???????
        ScrollBar.horizontal.policy: ScrollBar.AlwaysOff
        ScrollBar.vertical.policy: ScrollBar.AsNeeded

        // ????2????????????????
        ScrollBar.vertical: ScrollBar {
            id: vScrollBar
            policy: ScrollBar.AsNeeded

            // ?????????????????
            size: scrollView.height / Math.max(scrollView.height, gridLayout.implicitHeight)
            position: scrollView.contentItem ? scrollView.contentItem.contentY /
                     Math.max(1, gridLayout.implicitHeight - scrollView.height) : 0

            background: Rectangle {
                color: "#f0f0f0"
                radius: 6
                implicitWidth: 14
                border.color: "#d0d0d0"
                border.width: 1
            }

            contentItem: Rectangle {
                color: vScrollBar.pressed ? "#4a90e2" :
                       vScrollBar.hovered ? "#6ba3f0" : "#8bb5f0"
                radius: 6
                implicitWidth: 12
                implicitHeight: 30

                // ????????
                Rectangle {
                    anchors.centerIn: parent
                    width: 8
                    height: 2
                    color: "white"
                    opacity: 0.7
                    radius: 1
                }
            }
        }

        // ????3???Column??GridLayout??????????
        Column {
            id: mainColumn
            width: scrollView.availableWidth
            spacing: 10

            // ??Grid???????????Column???????
            Grid {
                id: gridLayout
                width: parent.width
                columns: 2
                columnSpacing: 10
                rowSpacing: 10

                // ?????Grid?????implicitHeight
                property real cellWidth: (width - columnSpacing) / columns

                Repeater {
                    model: deviceModel
                    delegate: Rectangle {
                        // ????4??????????Layout.fillWidth
                        width: gridLayout.cellWidth
                        // ????5?????????????????
                        height: Math.max(100, headerRect.height + deviceListColumn.height + 36)

                        border.color: "black"
                        border.width: 1
                        radius: 8
                        property var lanIndex: deviceModel.index(index, 0)

                        // ????6?????????????????
                        onHeightChanged: {
                            // ??Grid????implicitHeight
                            gridLayout.implicitHeight = Qt.binding(function() {
                                var totalHeight = 0;
                                var rows = Math.ceil(deviceModel.rowCount() / gridLayout.columns);
                                for (var i = 0; i < deviceModel.rowCount(); i++) {
                                    var itemHeight = Math.max(100, 80); // ??????
                                    if (i % gridLayout.columns === 0) {
                                        totalHeight += itemHeight + (i > 0 ? gridLayout.rowSpacing : 0);
                                    }
                                }
                                return totalHeight;
                            });
                        }

                        Column {
                            id: colLayout
                            anchors.fill: parent
                            anchors.margins: 10
                            spacing: 8

                            Rectangle {
                                id: headerRect
                                width: parent.width * 0.75
                                height: 30  // ???????????
                                radius: 4
                                color: "#f5f5f5"
                                border.color: "#e0e0e0"
                                border.width: 1

                                Text {
                                    anchors.centerIn: parent
                                    text: "----" + lanName + "----"
                                    font.bold: true
                                    color: "#666666"
                                    font.pointSize: 10
                                }
                            }

                            // ????7???Column??ListView????????
                            Column {
                                id: deviceListColumn
                                width: parent.width
                                spacing: 4

                                // ????8???Repeater??DelegateModel?????????
                                Repeater {
                                    model: DelegateModel {
                                        model: deviceModel
                                        rootIndex: lanIndex
                                        delegate: Button {
                                            property bool isClicked: false
                                            width: deviceListColumn.width
                                            height: 28
                                            font.pixelSize: 12

                                            background: Rectangle {
                                                color: isClicked ? "#4CAF50" : "white"
                                                border.color: isClicked ? "#4CAF50" : "#cccccc"
                                                border.width: 2
                                                radius: 5

                                                // ??????
                                                Behavior on color {
                                                    ColorAnimation { duration: 150 }
                                                }
                                            }

                                            contentItem: Text {
                                                text: deviceName
                                                color: isClicked ? "white" : "black"
                                                horizontalAlignment: Text.AlignHCenter
                                                verticalAlignment: Text.AlignVCenter
                                            }

                                            onClicked: {
                                                isClicked = !isClicked
                                            }

                                            // ????
                                            onHoveredChanged: {
                                                if (hovered && !isClicked) {
                                                    background.color = "#f0f0f0"
                                                } else if (!hovered && !isClicked) {
                                                    background.color = "white"
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        }
    }

    // ?????????????????
    Rectangle {
        anchors.bottom: parent.bottom
        anchors.right: parent.right
        anchors.margins: 10
        width: 200
        height: 60
        color: "#f0f0f0"
        border.color: "#cccccc"
        border.width: 1
        radius: 4
        opacity: 0.8

        Column {
            anchors.centerIn: parent
            spacing: 2

            Text {
                text: "Grid Height: " + Math.round(gridLayout.implicitHeight)
                font.pixelSize: 10
                color: "#666666"
            }

            Text {
                text: "ScrollView Height: " + Math.round(scrollView.height)
                font.pixelSize: 10
                color: "#666666"
            }

            Text {
                text: "Items: " + deviceModel.rowCount()
                font.pixelSize: 10
                color: "#666666"
            }
        }
    }
}
