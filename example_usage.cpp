#include "noise_detector.h"
#include <iostream>
#include <vector>
#include <string>

int main() {
    std::cout << "杂音检测器使用示例" << std::endl;
    std::cout << "==================" << std::endl;
    
    // 创建检测器实例
    NoiseDetector detector;
    
    // 示例1: 人声数据检测
    std::cout << "\n示例1: 人声数据检测" << std::endl;
    std::string voiceFFT = "[0HZ 72.696335][62HZ 38.357399][125HZ 1.847786][187HZ 9.510880][250HZ 14.777546][312HZ 13.302412][375HZ 15.997389][437HZ 10.871351][500HZ 3.458400][562HZ 0.515057][625HZ 0.424571][687HZ 1.135890][750HZ 0.801759][812HZ 0.009276][875HZ 0.514308][937HZ 1.065289][1000HZ 1.795554][1062HZ 1.276746][1125HZ 0.190738][1187HZ 0.095359][1250HZ 0.074901][1312HZ 0.186846][1375HZ 0.042717][1437HZ 0.091427][1500HZ 0.194694]";
    
    NoiseDetector::DetectionResult result1 = detector.detect(voiceFFT);
    std::cout << "检测结果: " << NoiseDetector::resultToString(result1) << std::endl;
    
    // 示例2: 多频音数据检测
    std::cout << "\n示例2: 多频音数据检测" << std::endl;
    std::string multiToneFFT = "[0HZ 22.243832][62HZ 105.679863][125HZ 104.819855][187HZ 15.388605][250HZ 7.413316][312HZ 3.129080][375HZ 0.681633][437HZ 3.255230][500HZ 6.875191][562HZ 5.024438][625HZ 1.042125][687HZ 0.434773][750HZ 1.313926][812HZ 1.212825][875HZ 0.856032][937HZ 1.087133][1000HZ 0.745064][1062HZ 1.139788][1125HZ 1.456484][1187HZ 0.140167][1250HZ 0.215218][1312HZ 0.209814][1375HZ 0.801344][1437HZ 1.155768][1500HZ 1.195854]";
    
    NoiseDetector::DetectionResult result2 = detector.detect(multiToneFFT);
    std::cout << "检测结果: " << NoiseDetector::resultToString(result2) << std::endl;
    
    // 示例3: 静音数据检测
    std::cout << "\n示例3: 静音数据检测" << std::endl;
    std::string silenceFFT = "[0HZ 0.174041][62HZ 2.519287][125HZ 5.523865][187HZ 3.553955][250HZ 0.948219][312HZ 0.695332][375HZ 0.451473][437HZ 0.685312][500HZ 0.856895][562HZ 0.299064][625HZ 0.910488][687HZ 0.045253][750HZ 0.732965][812HZ 0.113261][875HZ 0.966800][937HZ 0.553334][1000HZ 0.462011][1062HZ 0.053680][1125HZ 0.659350][1187HZ 0.157269][1250HZ 0.239490][1312HZ 0.499281][1375HZ 0.017817][1437HZ 0.424058][1500HZ 0.073379]";
    
    NoiseDetector::DetectionResult result3 = detector.detect(silenceFFT);
    std::cout << "检测结果: " << NoiseDetector::resultToString(result3) << std::endl;
    
    // 示例4: 批量检测
    std::cout << "\n示例4: 批量检测" << std::endl;
    std::vector<std::string> batchData = {voiceFFT, multiToneFFT, silenceFFT};
    std::vector<NoiseDetector::DetectionResult> batchResults = detector.detectBatch(batchData);
    
    for (size_t i = 0; i < batchResults.size(); ++i) {
        std::cout << "批量检测 " << (i+1) << ": " << NoiseDetector::resultToString(batchResults[i]) << std::endl;
    }
    
    // 示例5: 自定义配置
    std::cout << "\n示例5: 自定义配置检测" << std::endl;
    NoiseDetector::DetectionConfig customConfig;
    customConfig.voiceEnergyThreshold = 30.0;      // 降低人声阈值
    customConfig.multiToneEnergyThreshold = 80.0;  // 降低多频音阈值
    customConfig.silenceEnergyThreshold = 5.0;     // 降低静音阈值
    customConfig.continuityFrames = 1;             // 不使用连续性过滤
    
    NoiseDetector customDetector(customConfig);
    
    // 使用自定义配置重新检测
    NoiseDetector::DetectionResult customResult1 = customDetector.detect(voiceFFT);
    NoiseDetector::DetectionResult customResult2 = customDetector.detect(multiToneFFT);
    NoiseDetector::DetectionResult customResult3 = customDetector.detect(silenceFFT);
    
    std::cout << "自定义配置 - 人声: " << NoiseDetector::resultToString(customResult1) << std::endl;
    std::cout << "自定义配置 - 多频音: " << NoiseDetector::resultToString(customResult2) << std::endl;
    std::cout << "自定义配置 - 静音: " << NoiseDetector::resultToString(customResult3) << std::endl;
    
    // 示例6: 连续检测和统计
    std::cout << "\n示例6: 连续检测和统计" << std::endl;
    detector.reset(); // 重置检测器状态
    
    // 模拟连续检测
    std::vector<std::string> continuousData = {
        voiceFFT, voiceFFT, multiToneFFT, voiceFFT, silenceFFT, 
        silenceFFT, voiceFFT, multiToneFFT, multiToneFFT, silenceFFT
    };
    
    for (size_t i = 0; i < continuousData.size(); ++i) {
        NoiseDetector::DetectionResult result = detector.detect(continuousData[i]);
        std::cout << "帧 " << (i+1) << ": " << NoiseDetector::resultToString(result) << std::endl;
    }
    
    // 获取统计信息
    std::map<std::string, int> stats = detector.getStatistics();
    std::cout << "\n检测统计:" << std::endl;
    for (const auto& pair : stats) {
        std::cout << pair.first << ": " << pair.second << " 次" << std::endl;
    }
    
    // 示例7: 边界情况测试
    std::cout << "\n示例7: 边界情况测试" << std::endl;
    
    // 空数据
    NoiseDetector::DetectionResult emptyResult = detector.detect("");
    std::cout << "空数据检测: " << NoiseDetector::resultToString(emptyResult) << std::endl;
    
    // 单一频率
    std::string singleFreqFFT = "[1000HZ 50.0]";
    NoiseDetector::DetectionResult singleResult = detector.detect(singleFreqFFT);
    std::cout << "单频检测: " << NoiseDetector::resultToString(singleResult) << std::endl;
    
    // 高能量数据
    std::string highEnergyFFT = "[500HZ 1000.0][1000HZ 800.0][1500HZ 600.0]";
    NoiseDetector::DetectionResult highEnergyResult = detector.detect(highEnergyFFT);
    std::cout << "高能量检测: " << NoiseDetector::resultToString(highEnergyResult) << std::endl;
    
    std::cout << "\n示例程序执行完成!" << std::endl;
    
    return 0;
}
