#pragma once
#include <libavutil/frame.h>
#include <algorithm>
#include <cmath>
#include <stdint.h>
#include <vector>

// 可靠的帧内马赛克检测器 - 专门解决漏报问题
class ReliableMosaicDetector {
private:
    struct Config {
        int block_cols = 20;
        int block_rows = 10;
        int base_threshold = 25;      // 基础阈值（放宽）
        int strict_threshold = 15;    // 严格阈值
        double error_ratio = 0.08;    // 错误比例阈值（放宽）
        int min_samples = 3;          // 每个块最少采样点数
        bool enable_multi_reference = true;  // 启用多基准点验证
        bool enable_adaptive_threshold = true; // 启用自适应阈值
    } config;

public:
    // 多点基准值提取（解决单点不可靠问题）
    bool extractMultipleBaseValues(AVFrame* frame, std::vector<std::pair<int,int>>& base_values) {
        if (!frame) return false;
        
        int block_w = frame->width / config.block_cols;
        int block_h = frame->height / config.block_rows;
        
        // 四个角落的基准点位置
        std::vector<std::pair<int, int>> reference_positions = {
            {0, 0},                                    // 左上角
            {config.block_cols-1, 0},                  // 右上角  
            {0, config.block_rows-1},                  // 左下角
            {config.block_cols-1, config.block_rows-1} // 右下角
        };
        
        std::vector<int> offsets = {0, 37, 73, 109}; // 对应的固定偏移量
        
        base_values.clear();
        
        for (size_t i = 0; i < reference_positions.size(); ++i) {
            auto& pos = reference_positions[i];
            int cx = pos.first * block_w + block_w / 2;
            int cy = pos.second * block_h + block_h / 2;
            
            // 多点采样提高可靠性
            std::vector<std::pair<int, int>> sample_offsets = {
                {0, 0}, {-block_w/4, 0}, {block_w/4, 0}, 
                {0, -block_h/4}, {0, block_h/4}
            };
            
            std::vector<int> r_samples, g_samples, b_samples;
            
            for (auto& offset : sample_offsets) {
                int sx = cx + offset.first;
                int sy = cy + offset.second;
                
                // 边界检查
                if (sx < 0 || sx >= frame->width || sy < 0 || sy >= frame->height) continue;
                
                int y_index = sy * frame->linesize[0] + sx;
                int u_index = (sy / 2) * frame->linesize[1] + (sx / 2);
                int v_index = (sy / 2) * frame->linesize[2] + (sx / 2);
                
                uint8_t Y = frame->data[0][y_index];
                uint8_t U = frame->data[1][u_index];
                uint8_t V = frame->data[2][v_index];
                
                // YUV转RGB
                int C = Y - 16, D = U - 128, E = V - 128;
                int r = std::min(255, std::max(0, (298 * C + 409 * E + 128) >> 8));
                int g = std::min(255, std::max(0, (298 * C - 100 * D - 208 * E + 128) >> 8));
                int b = std::min(255, std::max(0, (298 * C + 516 * D + 128) >> 8));
                
                r_samples.push_back(r);
                g_samples.push_back(g);
                b_samples.push_back(b);
            }
            
            if (r_samples.size() >= config.min_samples) {
                // 使用中位数减少噪声影响
                std::sort(r_samples.begin(), r_samples.end());
                std::sort(g_samples.begin(), g_samples.end());
                std::sort(b_samples.begin(), b_samples.end());
                
                int median_r = r_samples[r_samples.size() / 2];
                int median_g = g_samples[g_samples.size() / 2];
                int median_b = b_samples[b_samples.size() / 2];
                
                // 反推原始基准值（减去偏移）
                int base_r = (median_r - offsets[i] + 256) % 256;
                int base_g = (median_g - offsets[i] + 256) % 256;
                int base_b = (median_b - offsets[i] + 256) % 256;
                
                base_values.push_back({base_r, (base_g << 8) | base_b});
            }
        }
        
        return base_values.size() >= 2; // 至少需要2个有效基准点
    }
    
    // 基准值一致性验证
    std::pair<int, int> getConsistentBaseValue(const std::vector<std::pair<int,int>>& base_values) {
        if (base_values.empty()) return {-1, -1};
        if (base_values.size() == 1) return base_values[0];
        
        // 找到最一致的基准值
        std::vector<int> vote_count(base_values.size(), 0);
        int tolerance = 20; // 允许的差异
        
        for (size_t i = 0; i < base_values.size(); ++i) {
            for (size_t j = 0; j < base_values.size(); ++j) {
                if (i == j) continue;
                
                int r1 = base_values[i].first;
                int g1 = (base_values[i].second >> 8) & 0xFF;
                int b1 = base_values[i].second & 0xFF;
                
                int r2 = base_values[j].first;
                int g2 = (base_values[j].second >> 8) & 0xFF;
                int b2 = base_values[j].second & 0xFF;
                
                int diff = abs(r1 - r2) + abs(g1 - g2) + abs(b1 - b2);
                if (diff <= tolerance) {
                    vote_count[i]++;
                }
            }
        }
        
        // 返回得票最多的基准值
        int max_votes = 0;
        int best_index = 0;
        for (size_t i = 0; i < vote_count.size(); ++i) {
            if (vote_count[i] > max_votes) {
                max_votes = vote_count[i];
                best_index = i;
            }
        }
        
        return base_values[best_index];
    }
    
    // 自适应阈值计算
    int calculateAdaptiveThreshold(AVFrame* frame, int base_r, int base_g, int base_b) {
        if (!config.enable_adaptive_threshold) {
            return config.base_threshold;
        }
        
        // 采样几个预期正确的块，计算实际误差
        std::vector<int> errors;
        int block_w = frame->width / config.block_cols;
        int block_h = frame->height / config.block_rows;
        
        // 采样中心区域的几个块
        std::vector<std::pair<int, int>> test_blocks = {
            {config.block_cols/2, config.block_rows/2},
            {config.block_cols/2-1, config.block_rows/2},
            {config.block_cols/2+1, config.block_rows/2},
            {config.block_cols/2, config.block_rows/2-1},
            {config.block_cols/2, config.block_rows/2+1}
        };
        
        for (auto& block : test_blocks) {
            int x = block.first, y = block.second;
            if (x < 0 || x >= config.block_cols || y < 0 || y >= config.block_rows) continue;
            
            int cx = x * block_w + block_w / 2;
            int cy = y * block_h + block_h / 2;
            
            int y_index = cy * frame->linesize[0] + cx;
            int u_index = (cy / 2) * frame->linesize[1] + (cx / 2);
            int v_index = (cy / 2) * frame->linesize[2] + (cx / 2);
            
            uint8_t Y = frame->data[0][y_index];
            uint8_t U = frame->data[1][u_index];
            uint8_t V = frame->data[2][v_index];
            
            // YUV转RGB
            int C = Y - 16, D = U - 128, E = V - 128;
            int actual_r = std::min(255, std::max(0, (298 * C + 409 * E + 128) >> 8));
            int actual_g = std::min(255, std::max(0, (298 * C - 100 * D - 208 * E + 128) >> 8));
            int actual_b = std::min(255, std::max(0, (298 * C + 516 * D + 128) >> 8));
            
            // 计算预期值（简化的线性关系）
            int expected_r = (base_r + x * 7 + y * 11) % 256;
            int expected_g = (base_g + x * 11 + y * 13) % 256;
            int expected_b = (base_b + x * 13 + y * 17) % 256;
            
            int error = abs(actual_r - expected_r) + abs(actual_g - expected_g) + abs(actual_b - expected_b);
            errors.push_back(error);
        }
        
        if (errors.empty()) return config.base_threshold;
        
        // 计算误差的75分位数作为自适应阈值
        std::sort(errors.begin(), errors.end());
        int threshold = errors[errors.size() * 3 / 4] + 10; // 加一点余量
        
        // 限制阈值范围
        return std::min(std::max(threshold, config.strict_threshold), config.base_threshold * 2);
    }
    
    // 主检测函数
    bool detectMosaic(AVFrame* frame) {
        if (!frame) return true;
        
        // 1. 提取多个基准值
        std::vector<std::pair<int,int>> base_values;
        if (!extractMultipleBaseValues(frame, base_values)) {
            return true; // 无法提取基准值，视为异常
        }
        
        // 2. 获取一致的基准值
        auto consistent_base = getConsistentBaseValue(base_values);
        if (consistent_base.first == -1) {
            return true; // 基准值不一致，视为异常
        }
        
        int base_r = consistent_base.first;
        int base_g = (consistent_base.second >> 8) & 0xFF;
        int base_b = consistent_base.second & 0xFF;
        
        // 3. 计算自适应阈值
        int threshold = calculateAdaptiveThreshold(frame, base_r, base_g, base_b);
        
        // 4. 检测所有块
        int block_w = frame->width / config.block_cols;
        int block_h = frame->height / config.block_rows;
        int error_blocks = 0;
        int total_blocks = 0;
        
        for (int y = 0; y < config.block_rows; ++y) {
            for (int x = 0; x < config.block_cols; ++x) {
                // 跳过基准块
                if ((x == 0 && y == 0) || 
                    (x == config.block_cols-1 && y == 0) ||
                    (x == 0 && y == config.block_rows-1) ||
                    (x == config.block_cols-1 && y == config.block_rows-1)) {
                    continue;
                }
                
                total_blocks++;
                
                // 多点采样检测
                int abnormal_samples = 0;
                int total_samples = 0;
                
                std::vector<std::pair<int, int>> sample_offsets = {
                    {block_w/2, block_h/2},     // 中心
                    {block_w/4, block_h/4},     // 左上
                    {3*block_w/4, block_h/4},   // 右上
                    {block_w/4, 3*block_h/4},   // 左下
                    {3*block_w/4, 3*block_h/4}  // 右下
                };
                
                for (auto& offset : sample_offsets) {
                    int cx = x * block_w + offset.first;
                    int cy = y * block_h + offset.second;
                    
                    int y_index = cy * frame->linesize[0] + cx;
                    int u_index = (cy / 2) * frame->linesize[1] + (cx / 2);
                    int v_index = (cy / 2) * frame->linesize[2] + (cx / 2);
                    
                    uint8_t Y = frame->data[0][y_index];
                    uint8_t U = frame->data[1][u_index];
                    uint8_t V = frame->data[2][v_index];
                    
                    // YUV转RGB
                    int C = Y - 16, D = U - 128, E = V - 128;
                    int actual_r = std::min(255, std::max(0, (298 * C + 409 * E + 128) >> 8));
                    int actual_g = std::min(255, std::max(0, (298 * C - 100 * D - 208 * E + 128) >> 8));
                    int actual_b = std::min(255, std::max(0, (298 * C + 516 * D + 128) >> 8));
                    
                    // 计算预期值
                    int expected_r = (base_r + x * 7 + y * 11) % 256;
                    int expected_g = (base_g + x * 11 + y * 13) % 256;
                    int expected_b = (base_b + x * 13 + y * 17) % 256;
                    
                    int diff = abs(actual_r - expected_r) + abs(actual_g - expected_g) + abs(actual_b - expected_b);
                    
                    total_samples++;
                    if (diff > threshold) {
                        abnormal_samples++;
                    }
                }
                
                // 如果超过一半的采样点异常，则认为该块异常
                if (abnormal_samples > total_samples / 2) {
                    error_blocks++;
                }
            }
        }
        
        // 5. 判断是否有马赛克
        double actual_error_ratio = (double)error_blocks / total_blocks;
        return actual_error_ratio > config.error_ratio;
    }
};
