# 杂音检测器 (Noise Detector)

## 概述

这是一个基于频谱分析的杂音检测器，能够识别人声、多频音和静音状态。该检测器通过分析nearendFFT数据来判断音频信号的类型。

## 功能特性

- **人声检测**: 识别人类语音信号
- **多频音检测**: 识别包含多个频率成分的音频信号
- **静音检测**: 识别静音或低噪声环境
- **连续性过滤**: 通过多帧分析提高检测稳定性
- **可配置参数**: 支持自定义检测阈值和参数

## 检测原理

### 数据分析基础

通过分析提供的三种日志数据，发现了以下特征：

1. **人声特征**:
   - 能量主要集中在125Hz-4000Hz范围内
   - 低频成分(≤1000Hz)相对较强
   - 频谱峰值数量适中(2-6个)
   - 总能量通常在50-800之间

2. **多频音特征**:
   - 包含多个明显的频率峰值(≥3个)
   - 频率分布较为分散
   - 总能量较高(通常>100)
   - 可能存在规律的频率间隔(谐波特征)

3. **静音特征**:
   - 总能量较低(通常<50)
   - 频谱分布相对平坦
   - 没有明显的峰值
   - 最大能量与平均能量比值较小

### 检测算法

1. **FFT数据解析**: 解析`[频率HZ 能量值]`格式的数据
2. **能量计算**: 计算总能量和频率分布
3. **峰值检测**: 识别频谱中的局部最大值
4. **特征分析**: 基于峰值分布、能量集中度等特征进行分类
5. **连续性过滤**: 通过多帧一致性检查减少误判

## 使用方法

### 基本使用

```cpp
#include "noise_detector.h"

// 创建检测器实例
NoiseDetector detector;

// 检测单帧数据
std::string fftData = "[0HZ 72.696335][62HZ 38.357399][125HZ 1.847786]...";
NoiseDetector::DetectionResult result = detector.detect(fftData);

// 获取结果字符串
std::string resultStr = NoiseDetector::resultToString(result);
std::cout << "检测结果: " << resultStr << std::endl;
```

### 自定义配置

```cpp
// 创建自定义配置
NoiseDetector::DetectionConfig config;
config.voiceEnergyThreshold = 60.0;        // 人声能量阈值
config.multiToneEnergyThreshold = 120.0;   // 多频音能量阈值
config.silenceEnergyThreshold = 15.0;      // 静音能量阈值
config.minPeakCount = 3;                   // 最小峰值数量
config.continuityFrames = 5;               // 连续性检查帧数

// 使用自定义配置创建检测器
NoiseDetector detector(config);
```

### 批量检测

```cpp
std::vector<std::string> fftDataList = {
    "[0HZ 72.696335][62HZ 38.357399]...",
    "[0HZ 105.679863][62HZ 104.819855]...",
    // 更多FFT数据...
};

std::vector<NoiseDetector::DetectionResult> results = detector.detectBatch(fftDataList);
```

## 配置参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `voiceEnergyThreshold` | 50.0 | 人声检测的最小能量阈值 |
| `multiToneEnergyThreshold` | 100.0 | 多频音检测的最小能量阈值 |
| `silenceEnergyThreshold` | 10.0 | 静音判断的最大能量阈值 |
| `voiceFreqMin` | 125 | 人声频率范围最小值(Hz) |
| `voiceFreqMax` | 4000 | 人声频率范围最大值(Hz) |
| `minPeakCount` | 2 | 检测所需的最小峰值数量 |
| `peakRatio` | 3.0 | 峰值与平均能量的比例阈值 |
| `multiToneSpread` | 0.8 | 多频音频谱分布系数 |
| `multiToneMinFreqs` | 3 | 多频音最少频率成分数 |
| `continuityFrames` | 3 | 连续性检查的帧数 |

## 编译和运行

### 使用CMake编译

```bash
mkdir build
cd build
cmake ..
make
```

### 运行测试

```bash
./test_noise_detector
```

## 检测结果类型

- `SILENCE`: 静音或低噪声环境
- `VOICE`: 人声信号
- `MULTI_TONE`: 多频音信号
- `UNKNOWN_NOISE`: 未知类型的杂音

## 性能特点

- **实时性**: 单次检测通常在几十微秒内完成
- **准确性**: 基于实际数据特征设计，具有较高的识别准确率
- **稳定性**: 通过连续性过滤减少瞬时噪声的影响
- **可扩展性**: 支持参数调优和算法扩展

## 注意事项

1. **数据格式**: 输入数据必须是`[频率HZ 能量值]`格式
2. **参数调优**: 根据实际应用场景调整检测阈值
3. **连续性**: 建议连续多帧检测以提高稳定性
4. **频率范围**: 当前算法主要针对0-8000Hz范围设计

## 扩展建议

1. **机器学习**: 可以收集更多数据训练机器学习模型
2. **自适应阈值**: 根据环境噪声动态调整检测阈值
3. **更多特征**: 添加频谱质心、带宽等更多特征
4. **实时优化**: 针对实时应用进行性能优化

## 许可证

本项目采用MIT许可证，详见LICENSE文件。
