#include "ColorBlockDetectYUV_MppFrame.h"
#include <iostream>
#include <vector>
#include <fstream>

// 阈值调优工具
class ThresholdTuner {
private:
    struct TestResult {
        int threshold;
        int true_positives;   // 正确检测到马赛克
        int false_positives;  // 误报（正常帧被判为异常）
        int true_negatives;   // 正确识别正常帧
        int false_negatives;  // 漏报（马赛克帧被判为正常）
        
        double precision() const {
            return (true_positives + false_positives) > 0 ? 
                   (double)true_positives / (true_positives + false_positives) : 0.0;
        }
        
        double recall() const {
            return (true_positives + false_negatives) > 0 ? 
                   (double)true_positives / (true_positives + false_negatives) : 0.0;
        }
        
        double f1_score() const {
            double p = precision();
            double r = recall();
            return (p + r) > 0 ? 2 * p * r / (p + r) : 0.0;
        }
        
        double accuracy() const {
            int total = true_positives + false_positives + true_negatives + false_negatives;
            return total > 0 ? (double)(true_positives + true_negatives) / total : 0.0;
        }
    };

public:
    // 测试不同阈值的性能
    void testThresholds(const std::vector<MppFrame>& normal_frames,
                       const std::vector<MppFrame>& mosaic_frames) {
        
        std::cout << "=== 阈值调优测试 ===" << std::endl;
        std::cout << "正常帧数量: " << normal_frames.size() << std::endl;
        std::cout << "马赛克帧数量: " << mosaic_frames.size() << std::endl;
        std::cout << std::endl;
        
        // 测试阈值范围：5-50，步长5
        std::vector<TestResult> results;
        
        for (int threshold = 5; threshold <= 50; threshold += 5) {
            TestResult result = {0};
            result.threshold = threshold;
            
            // 测试正常帧
            for (const auto& frame : normal_frames) {
                bool detected_mosaic = ColorBlockDetectYUV_MppFrame(frame, 20, 10, threshold, 0.05);
                if (detected_mosaic) {
                    result.false_positives++;  // 误报
                } else {
                    result.true_negatives++;   // 正确识别正常
                }
            }
            
            // 测试马赛克帧
            for (const auto& frame : mosaic_frames) {
                bool detected_mosaic = ColorBlockDetectYUV_MppFrame(frame, 20, 10, threshold, 0.05);
                if (detected_mosaic) {
                    result.true_positives++;   // 正确检测马赛克
                } else {
                    result.false_negatives++;  // 漏报
                }
            }
            
            results.push_back(result);
        }
        
        // 输出结果
        printResults(results);
        
        // 找到最佳阈值
        auto best_result = findBestThreshold(results);
        std::cout << "\n=== 推荐阈值 ===" << std::endl;
        std::cout << "最佳阈值: " << best_result.threshold << std::endl;
        std::cout << "F1分数: " << best_result.f1_score() << std::endl;
        std::cout << "准确率: " << best_result.accuracy() << std::endl;
        std::cout << "精确率: " << best_result.precision() << std::endl;
        std::cout << "召回率: " << best_result.recall() << std::endl;
    }
    
    // 实时阈值建议
    int suggestThreshold(MppFrame sample_frame) {
        if (!sample_frame) return 22;
        
        // 分析当前帧的压缩质量
        int width = mpp_frame_get_width(sample_frame);
        int height = mpp_frame_get_height(sample_frame);
        int block_w = width / 20;
        int block_h = height / 10;
        
        MppBuffer buf = mpp_frame_get_buffer(sample_frame);
        if (!buf) return 22;
        
        const uint8_t* base = (const uint8_t*)mpp_buffer_get_ptr(buf);
        int y_stride = mpp_frame_get_hor_stride(sample_frame);
        const uint8_t* y_plane = base;
        const uint8_t* uv_plane = base + y_stride * height;
        
        // 采样几个块，评估压缩噪声水平
        std::vector<int> noise_levels;
        
        for (int test_y = 1; test_y < 9; test_y += 2) {
            for (int test_x = 1; test_x < 19; test_x += 2) {
                // 采样块内的方差，评估压缩噪声
                std::vector<int> y_values;
                for (int dy = 0; dy < block_h; dy += 4) {
                    for (int dx = 0; dx < block_w; dx += 4) {
                        int cx = test_x * block_w + dx;
                        int cy = test_y * block_h + dy;
                        int y_index = cy * y_stride + cx;
                        y_values.push_back(y_plane[y_index]);
                    }
                }
                
                if (y_values.size() > 1) {
                    // 计算方差
                    double mean = 0;
                    for (int val : y_values) mean += val;
                    mean /= y_values.size();
                    
                    double variance = 0;
                    for (int val : y_values) {
                        variance += (val - mean) * (val - mean);
                    }
                    variance /= y_values.size();
                    
                    noise_levels.push_back((int)sqrt(variance));
                }
            }
        }
        
        if (noise_levels.empty()) return 22;
        
        // 计算平均噪声水平
        double avg_noise = 0;
        for (int noise : noise_levels) avg_noise += noise;
        avg_noise /= noise_levels.size();
        
        // 基于噪声水平建议阈值
        if (avg_noise < 3) {
            return 15;  // 低噪声，可以用严格阈值
        } else if (avg_noise < 6) {
            return 22;  // 中等噪声，标准阈值
        } else if (avg_noise < 10) {
            return 30;  // 高噪声，放宽阈值
        } else {
            return 40;  // 很高噪声，很宽松的阈值
        }
    }

private:
    void printResults(const std::vector<TestResult>& results) {
        std::cout << "阈值\t准确率\t精确率\t召回率\tF1分数\t误报率\t漏报率" << std::endl;
        std::cout << "------------------------------------------------------------" << std::endl;
        
        for (const auto& result : results) {
            double false_positive_rate = result.false_positives / 
                (double)(result.false_positives + result.true_negatives);
            double false_negative_rate = result.false_negatives / 
                (double)(result.false_negatives + result.true_positives);
                
            printf("%d\t%.3f\t%.3f\t%.3f\t%.3f\t%.3f\t%.3f\n",
                   result.threshold,
                   result.accuracy(),
                   result.precision(),
                   result.recall(),
                   result.f1_score(),
                   false_positive_rate,
                   false_negative_rate);
        }
    }
    
    TestResult findBestThreshold(const std::vector<TestResult>& results) {
        TestResult best = results[0];
        double best_score = best.f1_score();
        
        for (const auto& result : results) {
            // 综合考虑F1分数和准确率
            double score = result.f1_score() * 0.7 + result.accuracy() * 0.3;
            if (score > best_score) {
                best_score = score;
                best = result;
            }
        }
        
        return best;
    }
};

// 使用示例
void example_usage() {
    ThresholdTuner tuner;
    
    // 假设你有一些测试帧
    std::vector<MppFrame> normal_frames;   // 正常的pattern视频帧
    std::vector<MppFrame> mosaic_frames;   // 有马赛克的帧
    
    // 加载测试数据...
    // loadTestFrames(normal_frames, mosaic_frames);
    
    // 运行阈值测试
    tuner.testThresholds(normal_frames, mosaic_frames);
    
    // 或者对单帧进行实时建议
    // MppFrame sample = getCurrentFrame();
    // int suggested = tuner.suggestThreshold(sample);
    // std::cout << "建议阈值: " << suggested << std::endl;
}

int main() {
    std::cout << "=== 色块检测阈值调优工具 ===" << std::endl;
    std::cout << std::endl;
    
    std::cout << "阈值建议总结:" << std::endl;
    std::cout << "• 高质量网络 (低延迟、低丢包): 15-20" << std::endl;
    std::cout << "• 一般网络环境 (推荐): 20-25" << std::endl;
    std::cout << "• 不稳定网络 (高延迟、丢包): 25-35" << std::endl;
    std::cout << "• 当前默认值: 22 (适合大多数场景)" << std::endl;
    std::cout << std::endl;
    
    std::cout << "YUV差值阈值说明:" << std::endl;
    std::cout << "• 计算方式: |Y-Y_exp| + |U-U_exp| + |V-V_exp|" << std::endl;
    std::cout << "• 理论范围: 0-765" << std::endl;
    std::cout << "• 实际范围: 通常在 0-100 之间" << std::endl;
    std::cout << "• 压缩影响: H.264/H.265压缩会引入 5-15 的误差" << std::endl;
    std::cout << std::endl;
    
    std::cout << "调优建议:" << std::endl;
    std::cout << "1. 从 22 开始测试" << std::endl;
    std::cout << "2. 如果误报多，增加到 25-30" << std::endl;
    std::cout << "3. 如果漏报多，减少到 18-20" << std::endl;
    std::cout << "4. 使用实际视频流进行测试验证" << std::endl;
    
    return 0;
}
