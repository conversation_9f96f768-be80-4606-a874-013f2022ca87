import QtQuick 2.12
import QtQuick.Controls 2.12
import QtQuick.Layouts 1.12
import QtQml.Models 2.12

// 专门解决TreeView contentHeight undefined问题的组件
Rectangle {
    id: root
    anchors.fill: parent
    color: "white"

    // 解决方案：使用ScrollView + 自定义高度计算
    ScrollView {
        id: scrollView
        anchors.fill: parent
        anchors.margins: 20

        // 关键1：禁用水平滚动，只保留垂直滚动
        ScrollBar.horizontal.policy: ScrollBar.AlwaysOff
        ScrollBar.vertical.policy: ScrollBar.AsNeeded

        // 关键2：自定义滚动条，确保响应内容变化
        ScrollBar.vertical: ScrollBar {
            id: vScrollBar
            policy: ScrollBar.AsNeeded
            
            // 动态计算滚动条大小和位置
            size: scrollView.height / Math.max(scrollView.height, contentColumn.implicitHeight)
            
            background: Rectangle {
                color: "#f0f0f0"
                radius: 6
                implicitWidth: 14
                border.color: "#d0d0d0"
                border.width: 1
            }

            contentItem: Rectangle {
                color: vScrollBar.pressed ? "#4a90e2" :
                       vScrollBar.hovered ? "#6ba3f0" : "#8bb5f0"
                radius: 6
                implicitWidth: 12
                implicitHeight: 30
            }
        }

        // 关键3：使用Column作为主容器，更好地处理动态高度
        Column {
            id: contentColumn
            width: scrollView.availableWidth
            spacing: 10

            // 使用Grid实现两列布局
            Grid {
                id: deviceGrid
                width: parent.width
                columns: 2
                columnSpacing: 10
                rowSpacing: 10
                
                property real cellWidth: (width - columnSpacing) / columns

                Repeater {
                    model: deviceModel
                    delegate: Rectangle {
                        width: deviceGrid.cellWidth
                        // 关键4：动态计算高度，监听子组件变化
                        height: deviceColumn.implicitHeight + 20
                        
                        border.color: "black"
                        border.width: 1
                        radius: 8
                        
                        property var lanIndex: deviceModel.index(index, 0)
                        
                        Column {
                            id: deviceColumn
                            anchors.fill: parent
                            anchors.margins: 10
                            spacing: 8

                            Rectangle {
                                id: headerRect
                                width: parent.width
                                height: 30
                                radius: 4
                                color: "#f5f5f5"
                                border.color: "#e0e0e0"
                                border.width: 1
                                
                                Text {
                                    anchors.centerIn: parent
                                    text: "----" + lanName + "----"
                                    font.bold: true
                                    color: "#666666"
                                    font.pointSize: 10
                                }
                            }

                            // 关键5：使用Column + Repeater替代ListView
                            // 这样可以避免ListView的contentHeight问题
                            Column {
                                id: deviceList
                                width: parent.width
                                spacing: 4
                                
                                // 关键6：手动计算并设置implicitHeight
                                property int itemCount: deviceRepeater.count
                                property int itemHeight: 28
                                implicitHeight: itemCount * (itemHeight + spacing) - (itemCount > 0 ? spacing : 0)
                                
                                Repeater {
                                    id: deviceRepeater
                                    model: DelegateModel {
                                        model: deviceModel
                                        rootIndex: lanIndex
                                        delegate: Button {
                                            property bool isClicked: false
                                            width: deviceList.width
                                            height: deviceList.itemHeight
                                            font.pixelSize: 12

                                            background: Rectangle {
                                                color: isClicked ? "#4CAF50" : "white"
                                                border.color: isClicked ? "#4CAF50" : "#cccccc"
                                                border.width: 2
                                                radius: 5

                                                Behavior on color {
                                                    ColorAnimation { duration: 150 }
                                                }
                                            }

                                            contentItem: Text {
                                                text: deviceName
                                                color: isClicked ? "white" : "black"
                                                horizontalAlignment: Text.AlignHCenter
                                                verticalAlignment: Text.AlignVCenter
                                            }

                                            onClicked: {
                                                isClicked = !isClicked
                                            }

                                            onHoveredChanged: {
                                                if (hovered && !isClicked) {
                                                    background.color = "#f0f0f0"
                                                } else if (!hovered && !isClicked) {
                                                    background.color = "white"
                                                }
                                            }
                                        }
                                    }
                                }
                                
                                // 关键7：监听模型变化，更新高度
                                Connections {
                                    target: deviceModel
                                    function onRowsInserted() { deviceList.updateHeight() }
                                    function onRowsRemoved() { deviceList.updateHeight() }
                                    function onModelReset() { deviceList.updateHeight() }
                                }
                                
                                function updateHeight() {
                                    implicitHeight = Qt.binding(function() {
                                        return itemCount * (itemHeight + spacing) - (itemCount > 0 ? spacing : 0)
                                    })
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // 调试信息面板
    Rectangle {
        anchors.bottom: parent.bottom
        anchors.right: parent.right
        anchors.margins: 10
        width: 250
        height: 80
        color: "#f8f8f8"
        border.color: "#cccccc"
        border.width: 1
        radius: 4
        opacity: 0.9
        
        Column {
            anchors.centerIn: parent
            spacing: 3
            
            Text {
                text: "Content Height: " + Math.round(contentColumn.implicitHeight)
                font.pixelSize: 10
                color: "#666666"
            }
            
            Text {
                text: "ScrollView Height: " + Math.round(scrollView.height)
                font.pixelSize: 10
                color: "#666666"
            }
            
            Text {
                text: "ScrollBar Size: " + Math.round(vScrollBar.size * 100) + "%"
                font.pixelSize: 10
                color: "#666666"
            }
            
            Text {
                text: "Model Items: " + deviceModel.rowCount()
                font.pixelSize: 10
                color: "#666666"
            }
        }
    }
}
