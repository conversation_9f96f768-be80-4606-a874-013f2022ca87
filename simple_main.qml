import QtQuick 2.9
import QtQuick.Window 2.2

// Window {
// visible: true
// width: 640
// height: 480
// title: qsTr("拖拽示例")

// Rectangle {
// id: draggableItem
// width: 100
// height: 100
// color: "lightblue"

// MouseArea {
// id: dragArea
// anchors.fill: parent
// drag.target: parent
// onPressed: {
// dragArea.drag.active = true
// }
// }

// Drag.active: dragArea.drag.active
// Drag.hotSpot.x: width / 2   Rectangle{
 Rectangle{
        Rectangle{
            color:"gray"
            y:100
            width:360
            height:80
            id:rect1
        }
    
        //切换状态
        Rectangle{
            color:"steelblue"
            width:360
            height:80
            id:rect
    
            MouseArea{
                anchors.fill: parent
                onClicked: {
                    console.log("dddd")
                    rect.state="move"
                    rect1.height=50
                    rect1.state="move"
                }
            }
    
                states:[
                    State{
                    name:"move"
                    PropertyChanges{
                        target:rect
                        y:250
                    }
                    PropertyChanges{
                        target:rect1
                        y:330
                    }
                }
    
                ]
    
                transitions: [
                    Transition {
                        PropertyAnimation{
                            properties: "y"
                            duration:5000
                        }
                    }
                ]
    
        }
    }