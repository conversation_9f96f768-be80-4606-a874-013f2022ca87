#pragma once
#include <libavutil/frame.h>
#include <algorithm>
#include <cmath>
#include <stdint.h>
#include <vector>
#include <queue>

// 增强版马赛克检测器
class EnhancedMosaicDetector {
private:
    struct DetectionConfig {
        int block_cols = 20;
        int block_rows = 10;
        int color_threshold = 15;
        double error_ratio = 0.05;
        int sync_period = 30;
        int checksum_blocks = 4;
        int temporal_window = 5;  // 时间窗口大小
        bool enable_temporal = true;
        bool enable_spatial = true;
        bool enable_checksum = true;
    } config;
    
    // 历史帧信息
    struct FrameInfo {
        int frame_id;
        int A_r, A_g, A_b;
        bool is_valid;
        double error_rate;
    };
    
    std::queue<FrameInfo> frame_history;
    int current_frame_id = 0;
    
public:
    // 检测同步标记
    bool checkSyncMarkers(AVFrame* frame) {
        if (!frame) return false;
        
        int width = frame->width;
        int height = frame->height;
        int block_w = width / config.block_cols;
        int block_h = height / config.block_rows;
        
        // 预期的同步值
        int expected_sync = (current_frame_id % config.sync_period) * 8;
        int tolerance = 20;
        
        // 检查四个角落的同步标记
        std::vector<std::pair<int, int>> sync_positions = {
            {block_w/8, block_h/8},           // 左上
            {width - block_w/8, block_h/8},   // 右上
            {block_w/8, height - block_h/8},  // 左下
            {width - block_w/8, height - block_h/8}  // 右下
        };
        
        int valid_syncs = 0;
        for (auto& pos : sync_positions) {
            int y_index = pos.second * frame->linesize[0] + pos.first;
            int u_index = (pos.second / 2) * frame->linesize[1] + (pos.first / 2);
            int v_index = (pos.second / 2) * frame->linesize[2] + (pos.first / 2);
            
            uint8_t Y = frame->data[0][y_index];
            uint8_t U = frame->data[1][u_index];
            uint8_t V = frame->data[2][v_index];
            
            // YUV转RGB
            int C = Y - 16, D = U - 128, E = V - 128;
            int r = std::min(255, std::max(0, (298 * C + 409 * E + 128) >> 8));
            int g = std::min(255, std::max(0, (298 * C - 100 * D - 208 * E + 128) >> 8));
            int b = std::min(255, std::max(0, (298 * C + 516 * D + 128) >> 8));
            
            int avg_color = (r + g + b) / 3;
            if (abs(avg_color - expected_sync) <= tolerance) {
                valid_syncs++;
            }
        }
        
        return valid_syncs >= 3; // 至少3个同步标记正确
    }
    
    // 检测校验和块
    bool checkChecksumBlocks(AVFrame* frame, int A_r, int A_g, int A_b) {
        if (!frame) return false;
        
        int width = frame->width;
        int height = frame->height;
        int block_w = width / config.block_cols;
        int block_h = height / config.block_rows;
        
        int expected_checksum = (A_r + A_g + A_b + current_frame_id * 7) % 256;
        int mid_row = config.block_rows / 2;
        int valid_checksums = 0;
        
        for (int i = 0; i < config.checksum_blocks; ++i) {
            int x = (config.block_cols / 2) + i - (config.checksum_blocks / 2);
            int cx = x * block_w + block_w / 2;
            int cy = mid_row * block_h + block_h / 2;
            
            int y_index = cy * frame->linesize[0] + cx;
            int u_index = (cy / 2) * frame->linesize[1] + (cx / 2);
            int v_index = (cy / 2) * frame->linesize[2] + (cx / 2);
            
            uint8_t Y = frame->data[0][y_index];
            uint8_t U = frame->data[1][u_index];
            uint8_t V = frame->data[2][v_index];
            
            // YUV转RGB
            int C = Y - 16, D = U - 128, E = V - 128;
            int r = std::min(255, std::max(0, (298 * C + 409 * E + 128) >> 8));
            int g = std::min(255, std::max(0, (298 * C - 100 * D - 208 * E + 128) >> 8));
            int b = std::min(255, std::max(0, (298 * C + 516 * D + 128) >> 8));
            
            int expected_r = (expected_checksum + i * 31) % 256;
            int expected_g = (expected_checksum + i * 37) % 256;
            int expected_b = (expected_checksum + i * 41) % 256;
            
            int diff = abs(r - expected_r) + abs(g - expected_g) + abs(b - expected_b);
            if (diff <= config.color_threshold * 3) {
                valid_checksums++;
            }
        }
        
        return valid_checksums >= (config.checksum_blocks * 0.75); // 至少75%正确
    }
    
    // 时间序列一致性检查
    bool checkTemporalConsistency() {
        if (frame_history.size() < 2) return true;
        
        std::vector<FrameInfo> recent_frames;
        auto temp_queue = frame_history;
        while (!temp_queue.empty()) {
            recent_frames.push_back(temp_queue.front());
            temp_queue.pop();
        }
        
        // 检查帧ID连续性
        for (size_t i = 1; i < recent_frames.size(); ++i) {
            int expected_id = recent_frames[i-1].frame_id + 1;
            if (recent_frames[i].frame_id != expected_id) {
                return false; // 帧序列不连续
            }
        }
        
        // 检查基准值变化规律
        if (recent_frames.size() >= 2) {
            auto& prev = recent_frames[recent_frames.size()-2];
            auto& curr = recent_frames[recent_frames.size()-1];
            
            // 验证基准值变化是否符合预期
            int expected_A_r = (curr.frame_id * 13 + curr.frame_id * curr.frame_id % 97) % 256;
            int expected_A_g = (curr.frame_id * 13 * 17 + curr.frame_id % 127) % 256;
            int expected_A_b = (curr.frame_id * 13 * 19 + (curr.frame_id * 23) % 139) % 256;
            
            int diff_r = abs(curr.A_r - expected_A_r);
            int diff_g = abs(curr.A_g - expected_A_g);
            int diff_b = abs(curr.A_b - expected_A_b);
            
            return (diff_r + diff_g + diff_b) <= 30; // 允许一定误差
        }
        
        return true;
    }
    
    // 主检测函数
    bool detectMosaic(AVFrame* frame) {
        if (!frame) return true; // 空帧视为异常
        
        bool has_mosaic = false;
        
        // 1. 基础空间规律检测（你原有的逻辑）
        if (config.enable_spatial) {
            has_mosaic |= detectSpatialPattern(frame);
        }
        
        // 2. 同步标记检测
        if (config.enable_temporal) {
            has_mosaic |= !checkSyncMarkers(frame);
        }
        
        // 3. 获取基准值并检测校验和
        int A_r, A_g, A_b;
        if (extractBaseValues(frame, A_r, A_g, A_b)) {
            if (config.enable_checksum) {
                has_mosaic |= !checkChecksumBlocks(frame, A_r, A_g, A_b);
            }
            
            // 4. 更新历史记录
            updateFrameHistory(A_r, A_g, A_b, !has_mosaic);
        } else {
            has_mosaic = true; // 无法提取基准值
        }
        
        // 5. 时间序列一致性检查
        if (config.enable_temporal) {
            has_mosaic |= !checkTemporalConsistency();
        }
        
        current_frame_id++;
        return has_mosaic;
    }
    
private:
    bool extractBaseValues(AVFrame* frame, int& A_r, int& A_g, int& A_b) {
        // 从左上角非同步标记区域提取基准值
        int block_w = frame->width / config.block_cols;
        int block_h = frame->height / config.block_rows;
        
        // 采样左上角色块的中心区域（避开同步标记）
        int cx = block_w * 3 / 4;
        int cy = block_h * 3 / 4;
        
        int y_index = cy * frame->linesize[0] + cx;
        int u_index = (cy / 2) * frame->linesize[1] + (cx / 2);
        int v_index = (cy / 2) * frame->linesize[2] + (cx / 2);
        
        uint8_t Y = frame->data[0][y_index];
        uint8_t U = frame->data[1][u_index];
        uint8_t V = frame->data[2][v_index];
        
        // YUV转RGB
        int C = Y - 16, D = U - 128, E = V - 128;
        A_r = std::min(255, std::max(0, (298 * C + 409 * E + 128) >> 8));
        A_g = std::min(255, std::max(0, (298 * C - 100 * D - 208 * E + 128) >> 8));
        A_b = std::min(255, std::max(0, (298 * C + 516 * D + 128) >> 8));
        
        return true;
    }
    
    bool detectSpatialPattern(AVFrame* frame) {
        // 这里实现你原有的空间规律检测逻辑
        // 为了简化，这里返回false，实际应该实现完整的检测
        return false;
    }
    
    void updateFrameHistory(int A_r, int A_g, int A_b, bool is_valid) {
        FrameInfo info;
        info.frame_id = current_frame_id;
        info.A_r = A_r;
        info.A_g = A_g;
        info.A_b = A_b;
        info.is_valid = is_valid;
        
        frame_history.push(info);
        
        // 保持历史窗口大小
        if (frame_history.size() > config.temporal_window) {
            frame_history.pop();
        }
    }
};
