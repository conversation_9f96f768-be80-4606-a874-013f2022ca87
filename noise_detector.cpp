#include "noise_detector.h"
#include <sstream>
#include <algorithm>
#include <numeric>
#include <cmath>
#include <regex>

NoiseDetector::NoiseDetector(const DetectionConfig& config) 
    : config_(config) {
    recentResults_.reserve(maxHistorySize_);
}

void NoiseDetector::setConfig(const DetectionConfig& config) {
    config_ = config;
}

const NoiseDetector::DetectionConfig& NoiseDetector::getConfig() const {
    return config_;
}

std::vector<NoiseDetector::FrequencyData> NoiseDetector::parseFFTData(const std::string& fftString) {
    std::vector<FrequencyData> result;
    
    // 使用正则表达式解析 [频率HZ 能量值] 格式
    std::regex pattern(R"(\[(\d+)HZ\s+([\d.]+)\])");
    std::sregex_iterator iter(fftString.begin(), fftString.end(), pattern);
    std::sregex_iterator end;
    
    for (; iter != end; ++iter) {
        const std::smatch& match = *iter;
        FrequencyData data;
        data.frequency = std::stoi(match[1].str());
        data.energy = std::stod(match[2].str());
        result.push_back(data);
    }
    
    return result;
}

double NoiseDetector::calculateTotalEnergy(const std::vector<FrequencyData>& fftData) {
    double totalEnergy = 0.0;
    for (const auto& data : fftData) {
        totalEnergy += data.energy;
    }
    return totalEnergy;
}

std::vector<NoiseDetector::FrequencyData> NoiseDetector::detectPeaks(const std::vector<FrequencyData>& fftData) {
    std::vector<FrequencyData> peaks;
    
    if (fftData.size() < 3) return peaks;
    
    // 计算平均能量
    double avgEnergy = calculateTotalEnergy(fftData) / fftData.size();
    double peakThreshold = avgEnergy * config_.peakRatio;
    
    // 检测局部峰值
    for (size_t i = 1; i < fftData.size() - 1; ++i) {
        const auto& current = fftData[i];
        const auto& prev = fftData[i-1];
        const auto& next = fftData[i+1];
        
        // 检查是否为局部最大值且超过阈值
        if (current.energy > prev.energy && 
            current.energy > next.energy && 
            current.energy > peakThreshold) {
            peaks.push_back(current);
        }
    }
    
    // 按能量排序，取前几个最强的峰值
    std::sort(peaks.begin(), peaks.end(), 
              [](const FrequencyData& a, const FrequencyData& b) {
                  return a.energy > b.energy;
              });
    
    if (peaks.size() > 10) {
        peaks.resize(10);  // 最多保留10个峰值
    }
    
    return peaks;
}

bool NoiseDetector::analyzeVoiceCharacteristics(const std::vector<FrequencyData>& fftData, 
                                               const std::vector<FrequencyData>& peaks) {
    if (peaks.size() < config_.minPeakCount) {
        return false;
    }
    
    // 检查峰值是否主要集中在人声频率范围内
    int voicePeakCount = 0;
    double voiceEnergySum = 0.0;
    
    for (const auto& peak : peaks) {
        if (peak.frequency >= config_.voiceFreqMin && peak.frequency <= config_.voiceFreqMax) {
            voicePeakCount++;
            voiceEnergySum += peak.energy;
        }
    }
    
    // 人声特征：
    // 1. 大部分峰值在人声频率范围内
    // 2. 能量集中度较高
    // 3. 低频成分相对较强
    bool hasVoiceFreqDistribution = (double)voicePeakCount / peaks.size() >= 0.6;
    bool hasVoiceEnergy = voiceEnergySum >= config_.voiceEnergyThreshold;
    
    // 检查低频能量占比（人声通常在低频有较强成分）
    double lowFreqEnergy = 0.0;
    double totalEnergy = calculateTotalEnergy(fftData);
    
    for (const auto& data : fftData) {
        if (data.frequency <= 1000) {  // 1kHz以下
            lowFreqEnergy += data.energy;
        }
    }
    
    bool hasLowFreqDominance = (lowFreqEnergy / totalEnergy) >= 0.3;
    
    return hasVoiceFreqDistribution && hasVoiceEnergy && hasLowFreqDominance;
}

bool NoiseDetector::analyzeMultiToneCharacteristics(const std::vector<FrequencyData>& fftData,
                                                   const std::vector<FrequencyData>& peaks) {
    if (peaks.size() < config_.multiToneMinFreqs) {
        return false;
    }
    
    // 多频音特征：
    // 1. 有多个明显的峰值
    // 2. 峰值分布较为分散
    // 3. 总能量较高
    // 4. 可能有特定的频率间隔
    
    double totalEnergy = calculateTotalEnergy(fftData);
    if (totalEnergy < config_.multiToneEnergyThreshold) {
        return false;
    }
    
    // 检查峰值分布的分散程度
    std::vector<int> peakFreqs;
    for (const auto& peak : peaks) {
        peakFreqs.push_back(peak.frequency);
    }
    
    std::sort(peakFreqs.begin(), peakFreqs.end());
    
    // 计算频率分布的标准差
    double meanFreq = std::accumulate(peakFreqs.begin(), peakFreqs.end(), 0.0) / peakFreqs.size();
    double variance = 0.0;
    for (int freq : peakFreqs) {
        variance += (freq - meanFreq) * (freq - meanFreq);
    }
    variance /= peakFreqs.size();
    double stdDev = std::sqrt(variance);
    
    // 多频音的频率分布应该比较分散
    bool hasWideSpread = stdDev > 500.0;  // 标准差大于500Hz
    
    // 检查是否有规律的频率间隔（可能是谐波）
    bool hasRegularSpacing = false;
    if (peakFreqs.size() >= 3) {
        std::vector<int> intervals;
        for (size_t i = 1; i < peakFreqs.size(); ++i) {
            intervals.push_back(peakFreqs[i] - peakFreqs[i-1]);
        }
        
        // 检查间隔的一致性
        double meanInterval = std::accumulate(intervals.begin(), intervals.end(), 0.0) / intervals.size();
        int consistentIntervals = 0;
        for (int interval : intervals) {
            if (std::abs(interval - meanInterval) < meanInterval * 0.2) {  // 20%的容差
                consistentIntervals++;
            }
        }
        hasRegularSpacing = (double)consistentIntervals / intervals.size() >= 0.7;
    }
    
    return hasWideSpread || hasRegularSpacing;
}

bool NoiseDetector::isSilence(const std::vector<FrequencyData>& fftData, double totalEnergy) {
    // 静音判断：
    // 1. 总能量低
    // 2. 没有明显的峰值
    // 3. 能量分布相对平坦
    
    if (totalEnergy > config_.silenceEnergyThreshold) {
        return false;
    }
    
    // 检查能量分布的平坦程度
    if (fftData.empty()) return true;
    
    double maxEnergy = 0.0;
    double avgEnergy = totalEnergy / fftData.size();
    
    for (const auto& data : fftData) {
        maxEnergy = std::max(maxEnergy, data.energy);
    }
    
    // 如果最大能量与平均能量的比值不大，说明分布较平坦
    double energyRatio = (avgEnergy > 0) ? (maxEnergy / avgEnergy) : 0;
    
    return energyRatio < 5.0;  // 峰值不超过平均值的5倍
}

NoiseDetector::DetectionResult NoiseDetector::applyContinuityFilter(DetectionResult currentResult) {
    recentResults_.push_back(currentResult);
    
    if (recentResults_.size() > maxHistorySize_) {
        recentResults_.erase(recentResults_.begin());
    }
    
    // 如果历史记录不够，直接返回当前结果
    if (recentResults_.size() < config_.continuityFrames) {
        return currentResult;
    }
    
    // 检查最近几帧的一致性
    int recentSize = std::min((int)recentResults_.size(), config_.continuityFrames);
    std::map<DetectionResult, int> resultCount;
    
    for (int i = recentResults_.size() - recentSize; i < recentResults_.size(); ++i) {
        resultCount[recentResults_[i]]++;
    }
    
    // 找到最频繁的结果
    DetectionResult mostFrequent = DetectionResult::SILENCE;
    int maxCount = 0;
    
    for (const auto& pair : resultCount) {
        if (pair.second > maxCount) {
            maxCount = pair.second;
            mostFrequent = pair.first;
        }
    }
    
    // 如果最频繁的结果占主导地位，返回它
    if (maxCount >= (recentSize + 1) / 2) {
        return mostFrequent;
    }
    
    return currentResult;
}

NoiseDetector::DetectionResult NoiseDetector::detect(const std::string& nearendFFT) {
    // 解析FFT数据
    std::vector<FrequencyData> fftData = parseFFTData(nearendFFT);

    if (fftData.empty()) {
        return DetectionResult::SILENCE;
    }

    // 计算总能量
    double totalEnergy = calculateTotalEnergy(fftData);

    // 首先检查是否为静音
    if (isSilence(fftData, totalEnergy)) {
        return applyContinuityFilter(DetectionResult::SILENCE);
    }

    // 检测峰值
    std::vector<FrequencyData> peaks = detectPeaks(fftData);

    // 分析人声特征
    if (analyzeVoiceCharacteristics(fftData, peaks)) {
        return applyContinuityFilter(DetectionResult::VOICE);
    }

    // 分析多频音特征
    if (analyzeMultiToneCharacteristics(fftData, peaks)) {
        return applyContinuityFilter(DetectionResult::MULTI_TONE);
    }

    // 如果能量较高但不符合人声或多频音特征，归类为未知杂音
    if (totalEnergy > config_.silenceEnergyThreshold * 2) {
        return applyContinuityFilter(DetectionResult::UNKNOWN_NOISE);
    }

    return applyContinuityFilter(DetectionResult::SILENCE);
}

std::vector<NoiseDetector::DetectionResult> NoiseDetector::detectBatch(const std::vector<std::string>& fftDataList) {
    std::vector<DetectionResult> results;
    results.reserve(fftDataList.size());

    for (const auto& fftData : fftDataList) {
        results.push_back(detect(fftData));
    }

    return results;
}

void NoiseDetector::reset() {
    recentResults_.clear();
}

std::string NoiseDetector::resultToString(DetectionResult result) {
    switch (result) {
        case DetectionResult::SILENCE:
            return "SILENCE";
        case DetectionResult::VOICE:
            return "VOICE";
        case DetectionResult::MULTI_TONE:
            return "MULTI_TONE";
        case DetectionResult::UNKNOWN_NOISE:
            return "UNKNOWN_NOISE";
        default:
            return "UNKNOWN";
    }
}

std::map<std::string, int> NoiseDetector::getStatistics() const {
    std::map<std::string, int> stats;

    for (DetectionResult result : recentResults_) {
        std::string resultStr = resultToString(result);
        stats[resultStr]++;
    }

    return stats;
}
