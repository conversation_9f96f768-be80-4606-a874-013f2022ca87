cmake_minimum_required(VERSION 3.10)
project(NoiseDetector)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译选项
if(MSVC)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W4")
else()
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -O2")
endif()

# 添加头文件目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR})

# 创建杂音检测器库
add_library(noise_detector STATIC
    noise_detector.h
    noise_detector.cpp
)

# 创建测试程序
add_executable(test_noise_detector
    test_noise_detector.cpp
)

# 创建示例程序
add_executable(example_usage
    example_usage.cpp
)

# 链接库
target_link_libraries(test_noise_detector noise_detector)
target_link_libraries(example_usage noise_detector)

# 如果需要，可以添加安装规则
install(TARGETS noise_detector test_noise_detector example_usage
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
)

install(FILES noise_detector.h
    DESTINATION include
)
