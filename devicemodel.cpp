#include "devicemodel.h"


DeviceTreeModel::DeviceTreeModel(QObject *parent) : QAbstractItemModel(parent) {
    m_rootItem = new TreeItem();   // 根节点不显示
}

DeviceTreeModel::~DeviceTreeModel() { delete m_rootItem; }

QModelIndex DeviceTreeModel::index(int row, int column, const QModelIndex &parent) const
{
    if (!hasIndex(row, column, parent))
        return QModelIndex();

    TreeItem *pParentItem = nullptr;

    if (!parent.isValid()) { // parent 是一个无效的索引，证明它是根Item;
        pParentItem = m_rootItem;
    } else {
        pParentItem = (TreeItem *)parent.internalPointer();
    }

    TreeItem *pChildItem = pParentItem->child(row);

    // 得到指定parent的第 row 个Item的 Model index;
    if (pChildItem)
        return createIndex(row, column, pChildItem);//internalPointer返回的值在此设置

    return QModelIndex();
}

QModelIndex DeviceTreeModel::parent(const QModelIndex &child) const
{
    if (!child.isValid())
        return QModelIndex();

    TreeItem *pChildItem = static_cast<TreeItem*>(child.internalPointer());
    TreeItem *pParentItem = pChildItem->parentItem();

    // 根Item有一个无效的index;
    if (pParentItem == m_rootItem)
        return QModelIndex();
    // 自己作为parent（相对 index）, 但也是别人的child;
    return createIndex(pParentItem->row(), 0, pParentItem);
}

void DeviceTreeModel::addDevice(const QString &lanName, const QString &deviceName)
{
    TreeItem *lanItem = nullptr;

    if (!m_lanGroupMap.contains(lanName)) {
        lanItem = new TreeItem(this->m_rootItem);
        lanItem->setData(TreeItem::LanType, lanName);
        m_lanGroupMap[lanName] = lanItem;
    } else {
        lanItem = m_lanGroupMap[lanName];
    }
    beginInsertRows(index(lanItem->row(), 0, QModelIndex()), lanItem->childCount(), lanItem->childCount());

    TreeItem *deviceItem = new TreeItem(lanItem);
    deviceItem->setData(TreeItem::DeviceType, deviceName);
    endInsertRows();

}

// 某个Item挂接多少子Item;
int DeviceTreeModel::rowCount(const QModelIndex &parent) const
{
    if(parent.column() > 0)
        return 0;
    TreeItem* parentItem;
    // 如果父索引无效，则说明我们指的是根节点
    if (!parent.isValid())
        parentItem = m_rootItem;
    else
        // 通过internalPointer()获取父节点对应的TreeItem对象
        parentItem = static_cast<TreeItem*>(parent.internalPointer());

    // 返回父节点的子节点个数
    return parentItem->childCount();
}

int DeviceTreeModel::columnCount(const QModelIndex &parent) const
{
//    return 1;

    if (parent.isValid())
        return static_cast<TreeItem*>(parent.internalPointer())->columnCount();
    else
        return m_rootItem->columnCount();
}

QVariant DeviceTreeModel::data(const QModelIndex &index, int role) const
{
    if(!index.isValid() /*|| role != Qt::DisplayRole*/)
        return QVariant();
    TreeItem *item = static_cast<TreeItem*>(index.internalPointer());

    return item->data();
}

QHash<int, QByteArray> DeviceTreeModel::roleNames() const
{
    QHash<int, QByteArray> roles;
    roles[Qt::UserRole + 1] = "lanName";    // 自定义role
    roles[Qt::UserRole + 2] = "deviceName"; // 自定义role
    return roles;
}

int DeviceTreeModel::getSize()
{
    return m_lanGroupMap.size();
}
