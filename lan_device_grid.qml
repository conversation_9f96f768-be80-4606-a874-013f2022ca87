import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Window 2.15
import QtQml.Models 2.15

Window {
    id: win
    width: 800
    height: 600
    visible: true
    title: "LAN Device Grid (Custom)"

    Rectangle {
        anchors.fill: parent
        color: "#f8f8f8"

        GridView {
            id: grid
            anchors.fill: parent
            anchors.margins: 20
            cellWidth: width / 2
            cellHeight: 150
            model: deviceModel

            delegate: Rectangle {
                width: grid.cellWidth - 10
                height: grid.cellHeight - 10
                color: "white"
                border.color: "#b0b0b0"
                border.width: 1
                radius: 8

                property var lanIndex: deviceModel.index(index, 0)

                Column {
                    anchors.fill: parent
                    anchors.margins: 10
                    spacing: 8

                    Rectangle {
                        width: parent.width
                        height: 30
                        color: "#1976d2"
                        radius: 4
                        Text {
                            anchors.centerIn: parent
                            text: "[ " + display + " ]"
                            color: "white"
                            font.bold: true
                            font.pixelSize: 14
                        }
                    }

                    ListView {
                        width: parent.width
                        height: parent.height - 38
                        spacing: 4
                        interactive: false
                        model: DelegateModel {
                            model: deviceModel
                            rootIndex: lanIndex
                            delegate: But<PERSON> {
                                width: parent.width
                                height: 28
                                text: display
                                font.pixelSize: 12
                                background: Rectangle {
                                    color: "#f7f7f7"
                                    border.color: "#e0e0e0"
                                    border.width: 1
                                    radius: 3
                                }
                            }
                        }
                    }
                }
            }
        }
    }
} 