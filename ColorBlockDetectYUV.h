#pragma once
#include <libavutil/frame.h>
#include <algorithm>
#include <cmath>
#include <stdint.h>
void YUVFrameToRGB(const AVFrame* yuvFrame, uint8_t* rgbData, int width, int height) {
    SwsContext* ctx = sws_getContext(
        width, height, (AVPixelFormat)yuvFrame->format,
        width, height, AV_PIX_FMT_RGB24,
        SWS_BILINEAR, nullptr, nullptr, nullptr
    );
    uint8_t* dst[4] = { rgbData, nullptr, nullptr, nullptr };
    int dst_linesize[4] = { width * 3, 0, 0, 0 };
    sws_scale(ctx, yuvFrame->data, yuvFrame->linesize, 0, height, dst, dst_linesize);
    sws_freeContext(ctx);
}
#include <stdint.h>
#include <algorithm>
#include "rk_mpi.h" // rkmpp头文件

inline bool ColorBlockDetectYUV(MppFrame frame) {
    if (!frame)
        return false;

    // 获取宽高
    int width = mpp_frame_get_width(frame);
    int height = mpp_frame_get_height(frame);

    // 色块参数
    int block_cols = 20;
    int block_rows = 10;
    int block_w = width / block_cols;
    int block_h = height / block_rows;
    int error_blocks = 0;
    int color_threshold = 15;
    double error_ratio = 0.05;

    // 获取YUV数据指针
    MppBuffer buf = mpp_frame_get_buffer(frame);
    if (!buf) return false;
    uint8_t* base = (uint8_t*)mpp_buffer_get_ptr(buf);

    int y_stride = mpp_frame_get_hor_stride(frame);
    int uv_stride = y_stride; // NV12/NV21通常UV stride等于Y stride

    uint8_t* y_plane = base;
    uint8_t* uv_plane = base + y_stride * height;

    // 采样左上角色块中心像素，推算A_r, A_g, A_b
    int ax = 0, ay = 0;
    int a_cx = ax * block_w + block_w / 2;
    int a_cy = ay * block_h + block_h / 2;

    int y_index = a_cy * y_stride + a_cx;
    int uv_row = a_cy / 2;
    int uv_col = a_cx / 2;
    int uv_index = uv_row * uv_stride + uv_col * 2;

    uint8_t Y = y_plane[y_index];
    uint8_t U = uv_plane[uv_index];
    uint8_t V = uv_plane[uv_index + 1];

    // 直接YUV转RGB，得到A_r, A_g, A_b
    int C = Y - 16;
    int D = U - 128;
    int E = V - 128;
    int A_r = std::min(255, std::max(0, (298 * C + 409 * E + 128) >> 8));
    int A_g = std::min(255, std::max(0, (298 * C - 100 * D - 208 * E + 128) >> 8));
    int A_b = std::min(255, std::max(0, (298 * C + 516 * D + 128) >> 8));

    // 检查所有色块
    for (int y = 0; y < block_rows; ++y) {
        for (int x = 0; x < block_cols; ++x) {
            int abnormal = 0;
            // 采样5点：中心+四角
            int sample_offsets[5][2] = {
                {block_w/2, block_h/2},
                {block_w/4, block_h/4},
                {3*block_w/4, block_h/4},
                {block_w/4, 3*block_h/4},
                {3*block_w/4, 3*block_h/4}
            };
            for (int s = 0; s < 5; ++s) {
                int cx = x * block_w + sample_offsets[s][0];
                int cy = y * block_h + sample_offsets[s][1];
                int y_index = cy * y_stride + cx;
                int uv_row = cy / 2;
                int uv_col = cx / 2;
                int uv_index = uv_row * uv_stride + uv_col * 2;
                uint8_t Y = y_plane[y_index];
                uint8_t U = uv_plane[uv_index];
                uint8_t V = uv_plane[uv_index + 1];

                int r_exp = (A_r + x * 13 + y * 17 + x * y * 7) % 256;
                int g_exp = (A_g + x * 19 + y * 23 + (x ^ y) * 11) % 256;
                int b_exp = (A_b + x * 29 + y * 31 + (x * x + y * y) * 3) % 256;

                int Y_exp = (66 * r_exp + 129 * g_exp + 25 * b_exp + 128) >> 8; Y_exp += 16;
                int U_exp = (-38 * r_exp - 74 * g_exp + 112 * b_exp + 128) >> 8; U_exp += 128;
                int V_exp = (112 * r_exp - 94 * g_exp - 18 * b_exp + 128) >> 8; V_exp += 128;

                Y_exp = std::min(255, std::max(0, Y_exp));
                U_exp = std::min(255, std::max(0, U_exp));
                V_exp = std::min(255, std::max(0, V_exp));

                int diff = abs(Y - Y_exp) + abs(U - U_exp) + abs(V - V_exp);
                if (diff > color_threshold) {
                    abnormal = 1;
                    break;
                }
            }
            if (abnormal) error_blocks++;
        }
    }
    if (error_blocks > block_cols * block_rows * error_ratio) {
        return true; // 异常
    }
    return false; // 正常
}
// 适用于YUV420P格式，pattern_video.cpp生成规律
// 返回true表示检测到异常（马赛克），false表示正常
inline bool ColorBlockDetectYUV(AVFrame* frame) {
    if (!frame)
        return false;
    static int block_cols = 20;
    static int block_rows = 10;

    int block_w = frame->width / block_cols;
    int block_h = frame->height / block_rows;
    int error_blocks = 0;
    int color_threshold = 15; 
    double error_ratio = 0.05; 

    // 1. 采样左上角色块中心像素，推算A_r, A_g, A_b
    int ax = 0, ay = 0;
    int a_cx = ax * block_w + block_w / 2;
    int a_cy = ay * block_h + block_h / 2;

    int y_index = a_cy * frame->linesize[0] + a_cx;
    int u_index = (a_cy / 2) * frame->linesize[1] + (a_cx / 2);
    int v_index = (a_cy / 2) * frame->linesize[2] + (a_cx / 2);

    uint8_t Y = frame->data[0][y_index];
    uint8_t U = frame->data[1][u_index];
    uint8_t V = frame->data[2][v_index];

    // 直接YUV转RGB，得到A_r, A_g, A_b
    int C = Y - 16;
    int D = U - 128;
    int E = V - 128;
    int A_r = std::min(255, std::max(0, (298 * C + 409 * E + 128) >> 8));
    int A_g = std::min(255, std::max(0, (298 * C - 100 * D - 208 * E + 128) >> 8));
    int A_b = std::min(255, std::max(0, (298 * C + 516 * D + 128) >> 8));

    // 2. 检查所有色块
    for (int y = 0; y < block_rows; ++y) {
        for (int x = 0; x < block_cols; ++x) {
            int abnormal = 0;
            // 采样5点：中心+四角
            int sample_offsets[5][2] = {
                {block_w/2, block_h/2},
                {block_w/4, block_h/4},
                {3*block_w/4, block_h/4},
                {block_w/4, 3*block_h/4},
                {3*block_w/4, 3*block_h/4}
            };
            for (int s = 0; s < 5; ++s) {
                int cx = x * block_w + sample_offsets[s][0];
                int cy = y * block_h + sample_offsets[s][1];
                int y_index = cy * frame->linesize[0] + cx;
                int u_index = (cy / 2) * frame->linesize[1] + (cx / 2);
                int v_index = (cy / 2) * frame->linesize[2] + (cx / 2);

                uint8_t Y = frame->data[0][y_index];
                uint8_t U = frame->data[1][u_index];
                uint8_t V = frame->data[2][v_index];

                int r_exp = (A_r + x * 13 + y * 17 + x * y * 7) % 256;
                int g_exp = (A_g + x * 19 + y * 23 + (x ^ y) * 11) % 256;
                int b_exp = (A_b + x * 29 + y * 31 + (x * x + y * y) * 3) % 256;

                int Y_exp = (66 * r_exp + 129 * g_exp + 25 * b_exp + 128) >> 8; Y_exp += 16;
                int U_exp = (-38 * r_exp - 74 * g_exp + 112 * b_exp + 128) >> 8; U_exp += 128;
                int V_exp = (112 * r_exp - 94 * g_exp - 18 * b_exp + 128) >> 8; V_exp += 128;

                Y_exp = std::min(255, std::max(0, Y_exp));
                U_exp = std::min(255, std::max(0, U_exp));
                V_exp = std::min(255, std::max(0, V_exp));

                int diff = abs(Y - Y_exp) + abs(U - U_exp) + abs(V - V_exp);
                if (diff > color_threshold) {
                    abnormal = 1;
                    break; // 只要有一个点异常就算异常色块
                }
            }
            if (abnormal) error_blocks++;
        }
    }
    if (error_blocks > block_cols * block_rows * error_ratio) {
        return true; // 异常
    }
    return false; // 正常
} 