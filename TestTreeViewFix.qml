import QtQuick 2.12
import QtQuick.Controls 2.12
import QtQuick.Window 2.12

// 测试TreeView滚动条修复的演示程序
ApplicationWindow {
    id: window
    width: 1000
    height: 700
    visible: true
    title: "TreeView滚动条修复演示"

    Row {
        anchors.fill: parent
        spacing: 10

        // 左侧：问题版本
        Rectangle {
            width: parent.width / 2 - 5
            height: parent.height
            color: "#ffebee"
            border.color: "#f44336"
            border.width: 2

            Text {
                anchors.top: parent.top
                anchors.horizontalCenter: parent.horizontalCenter
                anchors.margins: 10
                text: "❌ 问题版本 - contentHeight undefined"
                font.bold: true
                color: "#d32f2f"
            }

            // 模拟你原来的问题代码
            Flickable {
                id: problemFlickable
                anchors.fill: parent
                anchors.margins: 40
                anchors.topMargin: 50

                contentWidth: problemColumn.width
                contentHeight: problemColumn.height

                Column {
                    id: problemColumn
                    width: problemFlickable.width
                    spacing: 10

                    Repeater {
                        model: 8
                        delegate: Rectangle {
                            width: parent.width
                            height: treeView.contentHeight + 50 // 问题：contentHeight可能是undefined
                            color: "#ffffff"
                            border.color: "#cccccc"
                            border.width: 1
                            radius: 8

                            Column {
                                anchors.fill: parent
                                anchors.margins: 10
                                spacing: 5

                                Text {
                                    text: "LAN " + (index + 1)
                                    font.bold: true
                                    color: "#333333"
                                }

                                // 问题：TreeView的contentHeight经常是undefined
                                ListView {
                                    id: treeView
                                    width: parent.width
                                    height: contentHeight // 问题所在
                                    spacing: 2
                                    interactive: false

                                    model: 3 + index % 5 // 动态数量

                                    delegate: Rectangle {
                                        width: parent.width
                                        height: 25
                                        color: index % 2 ? "#f5f5f5" : "#ffffff"
                                        Text {
                                            anchors.centerIn: parent
                                            text: "设备 " + (index + 1)
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                ScrollBar.vertical: ScrollBar {
                    // 滚动条无法正确响应内容变化
                }
            }
        }

        // 右侧：修复版本
        Rectangle {
            width: parent.width / 2 - 5
            height: parent.height
            color: "#e8f5e8"
            border.color: "#4caf50"
            border.width: 2

            Text {
                anchors.top: parent.top
                anchors.horizontalCenter: parent.horizontalCenter
                anchors.margins: 10
                text: "✅ 修复版本 - 滚动条正常工作"
                font.bold: true
                color: "#388e3c"
            }

            // 修复版本
            ScrollView {
                id: fixedScrollView
                anchors.fill: parent
                anchors.margins: 40
                anchors.topMargin: 50

                ScrollBar.horizontal.policy: ScrollBar.AlwaysOff
                ScrollBar.vertical.policy: ScrollBar.AsNeeded

                ScrollBar.vertical: ScrollBar {
                    id: customScrollBar
                    policy: ScrollBar.AsNeeded

                    // 关键：动态计算滚动条大小
                    size: fixedScrollView.height / Math.max(fixedScrollView.height, fixedColumn.implicitHeight)

                    background: Rectangle {
                        color: "#f0f0f0"
                        radius: 6
                        implicitWidth: 14
                        border.color: "#d0d0d0"
                        border.width: 1
                    }

                    contentItem: Rectangle {
                        color: customScrollBar.pressed ? "#4a90e2" :
                               customScrollBar.hovered ? "#6ba3f0" : "#8bb5f0"
                        radius: 6
                        implicitWidth: 12
                        implicitHeight: 30
                    }
                }

                Column {
                    id: fixedColumn
                    width: fixedScrollView.availableWidth
                    spacing: 10

                    Repeater {
                        model: 8
                        delegate: Rectangle {
                            width: parent.width
                            // 关键：使用implicitHeight而不是contentHeight
                            height: deviceColumn.implicitHeight + 20
                            color: "#ffffff"
                            border.color: "#cccccc"
                            border.width: 1
                            radius: 8

                            Column {
                                id: deviceColumn
                                anchors.fill: parent
                                anchors.margins: 10
                                spacing: 5

                                Text {
                                    text: "LAN " + (index + 1)
                                    font.bold: true
                                    color: "#333333"
                                }

                                // 修复：使用Column + Repeater替代ListView
                                Column {
                                    id: deviceList
                                    width: parent.width
                                    spacing: 2

                                    property int itemCount: deviceRepeater.count
                                    property int itemHeight: 25
                                    // 手动计算高度
                                    implicitHeight: itemCount * (itemHeight + spacing)

                                    Repeater {
                                        id: deviceRepeater
                                        model: 3 + index % 5 // 动态数量

                                        delegate: Rectangle {
                                            width: deviceList.width
                                            height: deviceList.itemHeight
                                            color: index % 2 ? "#f5f5f5" : "#ffffff"
                                            border.color: "#e0e0e0"
                                            border.width: 1

                                            Text {
                                                anchors.centerIn: parent
                                                text: "设备 " + (index + 1)
                                                color: "#666666"
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // 底部说明
    Rectangle {
        anchors.bottom: parent.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        height: 80
        color: "#f5f5f5"
        border.color: "#cccccc"
        border.width: 1

        Column {
            anchors.centerIn: parent
            spacing: 5

            Text {
                anchors.horizontalCenter: parent.horizontalCenter
                text: "修复要点："
                font.bold: true
                font.pixelSize: 14
                color: "#333333"
            }

            Text {
                anchors.horizontalCenter: parent.horizontalCenter
                text: "1) 使用ScrollView替代Flickable  2) 用Column+Repeater替代ListView  3) 手动计算implicitHeight"
                font.pixelSize: 12
                color: "#666666"
                wrapMode: Text.WordWrap
            }

            Text {
                anchors.horizontalCenter: parent.horizontalCenter
                text: "4) 动态绑定滚动条size属性  5) 避免使用contentHeight属性"
                font.pixelSize: 12
                color: "#666666"
                wrapMode: Text.WordWrap
            }
        }
    }
}
