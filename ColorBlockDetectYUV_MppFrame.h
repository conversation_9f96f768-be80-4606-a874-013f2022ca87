#pragma once
#include <stdint.h>
#include <algorithm>
#include "rk_mpi.h"

// 色块检测（适用于MppFrame，NV12/YUV420SP，pattern_video.cpp最新YUV规律）
inline bool ColorBlockDetectYUV_MppFrame(MppFrame frame, int block_cols = 20, int block_rows = 10, int color_threshold = 10, double error_ratio = 0.05) {
    if (!frame) return false;
    int width = mpp_frame_get_width(frame);
    int height = mpp_frame_get_height(frame);
    int block_w = width / block_cols;
    int block_h = height / block_rows;
    int error_blocks = 0;

    // 获取YUV数据指针
    int y_stride = mpp_frame_get_hor_stride(frame);
    int uv_stride = y_stride;
    MppBuffer buf = mpp_frame_get_buffer(frame);
    if (!buf) return false;
    const uint8_t* base = (const uint8_t*)mpp_buffer_get_ptr(buf);
    const uint8_t* y_plane = base;
    const uint8_t* uv_plane = base + y_stride * height;

    // 采样左上角色块中心像素，作为A_Y, A_U, A_V
    int ax = 0, ay = 0;
    int a_cx = ax * block_w + block_w / 2;
    int a_cy = ay * block_h + block_h / 2;
    int y_index = a_cy * y_stride + a_cx;
    int uv_row = a_cy / 2;
    int uv_col = a_cx / 2;
    int uv_index = uv_row * uv_stride + uv_col * 2;
    int A_Y = y_plane[y_index];
    int A_U = uv_plane[uv_index];
    int A_V = uv_plane[uv_index + 1];

    // 检查所有色块
    for (int y = 0; y < block_rows; ++y) {
        for (int x = 0; x < block_cols; ++x) {
            int abnormal = 0;
            int sample_offsets[5][2] = {
                {block_w/2, block_h/2},
                {block_w/4, block_h/4},
                {3*block_w/4, block_h/4},
                {block_w/4, 3*block_h/4},
                {3*block_w/4, 3*block_h/4}
            };
            for (int s = 0; s < 5; ++s) {
                int cx = x * block_w + sample_offsets[s][0];
                int cy = y * block_h + sample_offsets[s][1];
                int y_index = cy * y_stride + cx;
                int uv_row = cy / 2;
                int uv_col = cx / 2;
                int uv_index = uv_row * uv_stride + uv_col * 2;
                int Y = y_plane[y_index];
                int U = uv_plane[uv_index];
                int V = uv_plane[uv_index + 1];

                int Y_exp = (A_Y + x * 13 + y * 17 + x * y * 7) % 220 + 16;
                int U_exp = (A_U + x * 19 + y * 23 + (x ^ y) * 11) % 225 + 16;
                int V_exp = (A_V + x * 29 + y * 31 + (x * x + y * y) * 3) % 225 + 16;

                int diff = abs(Y - Y_exp) + abs(U - U_exp) + abs(V - V_exp);
                if (diff > color_threshold) {
                    abnormal = 1;
                    break;
                }
            }
            if (abnormal) error_blocks++;
        }
    }
    if (error_blocks > block_cols * block_rows * error_ratio) {
        return true; // 异常
    }
    return false; // 正常
} 