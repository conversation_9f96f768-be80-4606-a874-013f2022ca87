#pragma once
#include <stdint.h>
#include <algorithm>
#include <libavutil/frame.h>

// 色块检测（适用于AVFrame*，YUV420P，pattern_video.cpp最新YUV规律）
inline bool ColorBlockDetectYUV_AVFrame(AVFrame* frame, int block_cols = 20, int block_rows = 10, int color_threshold = 10, double error_ratio = 0.05) {
    if (!frame) return false;
    int width = frame->width;
    int height = frame->height;
    int block_w = width / block_cols;
    int block_h = height / block_rows;
    int error_blocks = 0;

    const uint8_t* y_plane = frame->data[0];
    const uint8_t* u_plane = frame->data[1];
    const uint8_t* v_plane = frame->data[2];
    int y_stride = frame->linesize[0];
    int u_stride = frame->linesize[1];
    int v_stride = frame->linesize[2];

    // 采样左上角色块中心像素，作为A_Y, A_U, A_V
    int ax = 0, ay = 0;
    int a_cx = ax * block_w + block_w / 2;
    int a_cy = ay * block_h + block_h / 2;
    int y_index = a_cy * y_stride + a_cx;
    int u_index = (a_cy / 2) * u_stride + (a_cx / 2);
    int v_index = (a_cy / 2) * v_stride + (a_cx / 2);
    int A_Y = y_plane[y_index];
    int A_U = u_plane[u_index];
    int A_V = v_plane[v_index];

    // 检查所有色块
    for (int y = 0; y < block_rows; ++y) {
        for (int x = 0; x < block_cols; ++x) {
            int abnormal = 0;
            int sample_offsets[5][2] = {
                {block_w/2, block_h/2},
                {block_w/4, block_h/4},
                {3*block_w/4, block_h/4},
                {block_w/4, 3*block_h/4},
                {3*block_w/4, 3*block_h/4}
            };
            for (int s = 0; s < 5; ++s) {
                int cx = x * block_w + sample_offsets[s][0];
                int cy = y * block_h + sample_offsets[s][1];
                int y_index = cy * y_stride + cx;
                int u_index = (cy / 2) * u_stride + (cx / 2);
                int v_index = (cy / 2) * v_stride + (cx / 2);
                int Y = y_plane[y_index];
                int U = u_plane[u_index];
                int V = v_plane[v_index];

                int Y_exp = (A_Y + x * 13 + y * 17 + x * y * 7) % 220 + 16;
                int U_exp = (A_U + x * 19 + y * 23 + (x ^ y) * 11) % 225 + 16;
                int V_exp = (A_V + x * 29 + y * 31 + (x * x + y * y) * 3) % 225 + 16;

                int diff = abs(Y - Y_exp) + abs(U - U_exp) + abs(V - V_exp);
                if (diff > color_threshold) {
                    abnormal = 1;
                    break;
                }
            }
            if (abnormal) error_blocks++;
        }
    }
    if (error_blocks > block_cols * block_rows * error_ratio) {
        return true; // 异常
    }
    return false; // 正常
} 
